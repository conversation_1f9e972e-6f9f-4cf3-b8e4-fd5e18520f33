# 飞书 OAuth 登录功能配置指南

## 功能概述

本功能为 Archery 项目添加了飞书 OAuth 登录支持，包括：

1. **飞书 OAuth 登录**：用户可以通过飞书账号登录 Archery
2. **自动用户创建**：首次登录时自动创建用户账号
3. **组织架构同步**：根据飞书部门信息自动创建 Django 组并分配用户

## 配置步骤

### 1. 飞书开放平台配置

1. 访问 [飞书开放平台](https://open.feishu.cn/)
2. 创建企业自建应用
3. 获取应用的 `App ID` 和 `App Secret`
4. 配置重定向 URL：`https://your-domain.com/feishu/callback/`
5. 申请以下权限：
   - `contact:user.id:readonly` - 获取用户 ID
   - `contact:user.email:readonly` - 获取用户邮箱
   - `contact:user.employee_id:readonly` - 获取用户工号
   - `contact:department.info:readonly` - 获取部门信息

### 2. 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 启用飞书认证
ENABLE_FEISHU=true

# 飞书应用配置
AUTH_FEISHU_APP_ID=cli_xxxxxxxxxx
AUTH_FEISHU_APP_SECRET=your_app_secret
AUTH_FEISHU_REDIRECT_URI=https://your-domain.com/feishu/callback/
```

### 3. 系统配置

在 Archery 管理后台的【配置项管理】页面中配置：

- **FEISHU_BTN_NAME**：登录按钮显示名称（如：以飞书登录）
- **FEISHU_OAUTH_APP_ID**：飞书应用的 App ID
- **FEISHU_OAUTH_APP_SECRET**：飞书应用的 App Secret
- **FEISHU_OAUTH_REDIRECT_URI**：OAuth 回调地址

> 注意：系统配置的优先级高于环境变量配置

### 4. 重启服务

配置完成后重启 Archery 服务：

```bash
# 如果使用 Docker
docker-compose restart

# 如果使用 systemd
systemctl restart archery

# 如果使用 supervisor
supervisorctl restart archery
```

## 功能特性

### 用户创建逻辑

1. 首次登录时，系统会自动创建用户账号
2. 用户名规则：
   - 优先使用飞书工号（employee_id）
   - 如无工号，使用邮箱前缀
3. 用户信息同步：
   - 显示名称：飞书用户姓名
   - 邮箱：飞书用户邮箱
   - 飞书 Open ID：用于后续关联

### 组织架构同步

1. 获取用户所在的所有部门
2. 为每个部门创建 Django 组，格式：`飞书-一级部门-二级部门-三级部门`
3. 将用户添加到对应的组中
4. 支持多级部门结构（最多5级）

### 权限管理

- 新用户会自动添加到系统配置的默认权限组和资源组
- 可通过 Django 管理后台进一步配置用户权限

## 安全考虑

1. **状态参数验证**：使用 state 参数防止 CSRF 攻击
2. **令牌缓存**：应用访问令牌会缓存，避免频繁请求
3. **错误处理**：完善的错误处理和日志记录
4. **权限最小化**：只申请必要的 API 权限

## 故障排除

### 常见问题

1. **登录按钮不显示**
   - 检查 `ENABLE_FEISHU` 环境变量是否设置为 `true`
   - 确认服务已重启

2. **授权失败**
   - 检查 App ID 和 App Secret 是否正确
   - 确认回调地址配置正确
   - 检查飞书应用权限是否申请

3. **用户创建失败**
   - 检查数据库连接
   - 查看应用日志中的错误信息
   - 确认用户邮箱格式正确

4. **部门同步失败**
   - 检查是否有部门信息获取权限
   - 查看日志中的 API 调用错误

### 日志查看

```bash
# 查看应用日志
tail -f logs/archery.log

# 查看特定的飞书相关日志
grep "飞书" logs/archery.log
```

## API 接口

### 登录入口
- URL: `/feishu/login/`
- 方法: GET
- 功能: 重定向到飞书授权页面

### 回调处理
- URL: `/feishu/callback/`
- 方法: GET
- 参数: `code`, `state`
- 功能: 处理飞书 OAuth 回调

## 开发说明

### 核心文件

- `common/utils/feishu_oauth.py` - 飞书 OAuth 工具类
- `common/authenticate/feishu_auth.py` - 飞书认证后端
- `common/views/feishu_views.py` - 飞书登录视图
- `archery/settings.py` - 配置项定义
- `archery/urls.py` - URL 路由配置

### 扩展开发

如需扩展功能，可以：

1. 修改 `FeishuOAuth` 类添加更多 API 调用
2. 在 `FeishuAuthenticationBackend` 中自定义用户创建逻辑
3. 扩展部门同步功能，支持更复杂的组织架构

## 版本兼容性

- Django 4.1+
- Python 3.8+
- 飞书开放平台 API v3

## 更新日志

- v1.0.0: 初始版本，支持基础 OAuth 登录和组织架构同步
