# https://django-environ.readthedocs.io/en/latest/quickstart.html#usage
# https://docs.djangoproject.com/zh-hans/4.1/ref/settings/
DEBUG=false
DATABASE_URL=mysql://root:@127.0.0.1:3306/archery
CACHE_URL=redis://127.0.0.1:6379/0

# https://django-auth-ldap.readthedocs.io/en/latest/
ENABLE_LDAP=false
AUTH_LDAP_ALWAYS_UPDATE_USER=true
AUTH_LDAP_USER_ATTR_MAP=username=cn,display=displayname,email=email

# https://docs.djangoproject.com/en/4.0/ref/settings/#csrf-trusted-origins
CSRF_TRUSTED_ORIGINS=http://127.0.0.1:9123

# https://django-q.readthedocs.io/en/latest/configure.html#
Q_CLUSTER_WORKERS=4
Q_CLUSTER_TIMEOUT=60
Q_CLUISTER_SYNC=false

# https://djangocas.dev/docs/latest/
ENABLE_CAS=true
CAS_SERVER_URL=https://127.0.0.1
CAS_VERSION=2
SECURE_SSL_REDIRECT=false