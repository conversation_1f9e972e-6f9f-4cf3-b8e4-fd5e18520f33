{% extends "base.html" %}
{% load format_tags %}
{% block content %}
    <!--有管理权限的用户可以直接编辑-->
    <div class="form-inline">
        <h4 style="display: inline;">工单名称：<span>{{ archive_config.title }}</span></h4>&nbsp;&nbsp;&nbsp;
        {% if perms.sql.archive_mgt %}
            <a type='button' id="btnEditArchiveConfig" class='btn btn-info'
               href="/admin/sql/archiveconfig/{{ archive_config.id }}/change/">修改归档配置</a>
        {% endif %}
        <!--单次调用-->
        {% if archive_config.status == 1 and perms.sql.archive_mgt %}
            <div class="form-group">
                <button id="btn_archive_once" type="button" class="btn btn-warning">
                    <span class="glyphicon glyphicon-ok" aria-hidden="true"></span>
                    <span aria-hidden="true"></span>立即归档
                </button>
            </div>
        {% endif %}
    </div>
    <input type="hidden" id="sqlMaxRowNumber" value="{{ rows|length }}">
    <input type="hidden" id="editSqlContent" value="{{ archive_config.sql_content }}"/>
    <hr>
    <h4>
        审批流
    </h4>
    <h5>
    {% include "workflow_display.html" %}
    <h4>
        其他信息
    </h4>
    <table data-toggle="table" class="table table-striped table-hover"
           style="table-layout:inherit;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">
        <thead>
        <tr>
            <th>
                申请人
            </th>
            <th>
                实例
            </th>
            <th>
                数据库
            </th>
            <th>
                表
            </th>
            <th>
                归档模式
            </th>
            <th>
                申请时间
            </th>
            <th>
                最近归档时间
            </th>
            <th>
                当前状态
            </th>
            <th>
                组
            </th>
        </tr>
        </thead>
        <tbody>
        <tr class="success">
            <td>
                {{ archive_config.user_display }}
            </td>
            <td>
                {{ archive_config.src_instance }}
            </td>
            <td>
                {{ archive_config.src_db_name }}
            </td>
            <td>
                {{ archive_config.src_table_name }}
            </td>
            <td>
                {{ archive_config.mode }}
            </td>
            <td>
                {{ archive_config.create_time }}
            </td>
            <td>
                {{ archive_config.last_archive_time }}
            </td>
            <td>
                {% if archive_config.status == 0 %}
                    <b style="color: red">待审核</b>
                {% elif archive_config.status == 1 %}
                    <b style="color: green">审核通过</b>
                {% elif archive_config.status == 2 %}
                    <b style="color: red">审核不通过</b>
                {% elif archive_config.status == 3 %}
                    <b style="color: red">审核取消</b>
                {% endif %}
            </td>
            <td>
                {{ archive_config.resource_group }}
            </td>
        </tr>
        </tbody>
    </table>
    <br>
    <!-- Nav tabs -->
    <ul id="nav-tabs" class="nav nav-tabs" role="tablist">
        <li id="detail_tab" class="active">
            <a href="#detail" role="tab" data-toggle="tab">归档配置信息</a>
        </li>
        <li id="logs_tab">
            <a href="#logs" role="tab" data-toggle="tab">归档日志</a>
        </li>
    </ul>
    <!-- Tab panes -->
    <div id="tab-content" class="tab-content">
        <!-- 归档配置信息-->
        <div id="detail" role="tabpanel" class="panel-body tab-pane fade in active form-group">
            {% if archive_config.status == 1 and perms.sql.archive_mgt %}
                <h4><b>是否启用</b></h4>
                <hr/>
                <div class="form-group">
                    <div class="left">
                        <div class="switch switch-small">
                            <label>
                                <input id="state"
                                       key="state"
                                       value="{{ archive_config.state }}"
                                       type="checkbox">
                            </label>
                        </div>
                    </div>
                </div>
            {% endif %}
            <h4><b>归档信息</b></h4>
            <hr/>
            <div class="form-horizontal">
                <div class="form-group form-inline">
                    <label class="col-sm-2 control-label">归档模式</label>
                    <div class="col-sm-10">
                        <p class="form-control-static">{% if archive_config.mode == 'file' %}归档到文件(file)
                        {% elif archive_config.mode == 'dest' %}归档到其他实例(dest)
                        {% elif archive_config.mode == 'purge' %}直接删除(purge)
                        {% endif %}</p>
                    </div>
                    <label class="col-sm-2 control-label">源数据</label>
                    <div class="col-sm-10">
                        <p class="form-control-static">{% if archive_config.no_delete %}保留{% else %}删除{% endif %}</p>
                    </div>
                    <label class="col-sm-2 control-label">休眠(秒)</label>
                    <div class="col-sm-10">
                        <p class="form-control-static">{{ archive_config.sleep }}</p>
                    </div>
                </div>
            </div>
            <h4><b>实例信息</b></h4>
            <hr/>
            <div class="form-horizontal col-sm-12">
                <div class="col-sm-6 left">
                    <h5 style="color: darkgrey"><b>源信息</b></h5>
                    <hr/>
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">源实例</label>
                            <div class="col-sm-9">
                                <p class="form-control-static">{{ archive_config.src_instance }}</p>
                            </div>
                            <label class="col-sm-3 control-label">源数据库</label>
                            <div class="col-sm-9">
                                <p class="form-control-static">{{ archive_config.src_db_name }}</p>
                            </div>
                            <label class="col-sm-3 control-label">源表</label>
                            <div class="col-sm-9">
                                <p class="form-control-static">{{ archive_config.src_table_name }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6 right">
                    <h5 style="color: darkgrey"><b>目标信息</b></h5>
                    <hr/>
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">目标实例</label>
                            <div class="col-sm-9">
                                <p class="form-control-static">{{ archive_config.dest_instance }}</p>
                            </div>
                            <label class="col-sm-3 control-label">目标数据库</label>
                            <div class="col-sm-9">
                                <p class="form-control-static">{{ archive_config.dest_db_name }}</p>
                            </div>
                            <label class="col-sm-3 control-label">目标表</label>
                            <div class="col-sm-9">
                                <p class="form-control-static">{{ archive_config.dest_table_name }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <h4><b>归档条件</b></h4>
            <hr/>
            <div class="form-horizontal  col-sm-12">
                <div class="form-group">
                    <div class="col-sm-9">
                        <p class="form-control-static"><code>{{ archive_config.condition }}</code></p>
                    </div>
                </div>
            </div>
        </div>
        <!-- 归档日志-->
        <div id="logs" role="tabpanel" class="tab-pane fade table-responsive">
            <table id="tb-logs" data-toggle="table" class="table table-hover"
                   style="table-layout:inherit;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">
            </table>
        </div>
    </div>
    <!--最后操作信息-->
    {% if last_operation_info %}
        <table data-toggle="table" class="table table-striped table-hover"
               style="table-layout:inherit;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">
            <thead>
            <tr>
                <th>
                    操作信息
                </th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>
                    {{ last_operation_info }}
                </td>
            </tr>
            </tbody>
        </table>
        <br>
    {% endif %}
    {% if archive_config.status == 0 %}
        {% if can_review %}
            <textarea id="remark" name="remark" class="form-control" data-name="审核备注"
                      placeholder="请填写审核备注" rows=3></textarea>
            <br>
            <form action="/archive/audit/" method="post" style="display:inline-block;">
                {% csrf_token %}
                <input type="hidden" name="archive_id" value="{{ archive_config.id }}">
                <input type="hidden" id="audit_status" name="audit_status" value="1">
                <input type="submit" id="btnPass" onclick="loading(this)" class="btn btn-success" value="审核通过"/>
            </form>

            <form id="form-cancel" action="/archive/audit/" method="post" style="display:inline-block;">
                {% csrf_token %}
                <input type="hidden" name="archive_id" value="{{ archive_config.id }}">
                <input type="hidden" id="audit_status" name="audit_status" value="2">
                <input type="hidden" id="audit_remark" name="audit_remark" value="">
                <input type="button" id="btnReject" class="btn btn-default" value="终止流程"/>
            </form>
        {% endif %}
    {% endif %}
{% endblock content %}

{% block js %}
    {% load static %}
    <link href="{% static 'bootstrap-switch/css/bootstrap-switch.min.css' %}" rel="stylesheet" type="text/css"/>
    <script src="{% static 'bootstrap-switch/js/bootstrap-switch.min.js' %}"></script>
    <script>
        // 系统设置checkbox事件
        $('input[type="checkbox"]').each(function (i) {
            if ($(this).val() === 'True') {
                $(this).bootstrapSwitch('state', true);
            } else {
                $(this).bootstrapSwitch('state', false);
            }
        });
        $('input[type="checkbox"]').on('switchChange.bootstrapSwitch', function (event, state) {
            // 修改状态
            $.ajax({
                type: "post",
                url: "/archive/switch/",
                dataType: "json",
                data: {
                    "archive_id": "{{ archive_config.id }}",
                    "state": state
                },
                complete: function () {
                },
                success: function (data) {
                    if (data.status === 0) {
                        if (state) {
                            $(this).val(true);
                            alert('已开启归档任务');
                        } else {
                            $(this).val(false);
                            alert('已关闭归档任务');
                        }
                    } else {
                        alert(data.msg);
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert(errorThrown);
                }
            });

        });

        // 单次归档
        $("#btn_archive_once").click(function () {
            let isContinue = confirm("请确认是否立即进行归档？");
            if (isContinue) {
                $.ajax({
                    type: "get",
                    url: "/archive/once/",
                    dataType: "json",
                    data: {
                        archive_id: "{{ archive_config.id }}"
                    },
                    complete: function () {
                    },
                    success: function (data) {
                        if (data.status === 0) {
                            alert('归档任务提交成功！请稍后查看归档日志获取结果');
                        } else {
                            alert(data.msg);
                        }
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alert(errorThrown);
                    }
                });
            }
        });


        // 获取日志日志
        function get_logs() {
            //初始化table
            $('#tb-logs').bootstrapTable('destroy').bootstrapTable({
                escape: true,
                method: 'get',
                contentType: "application/x-www-form-urlencoded",
                url: "/archive/log/",
                striped: true,                      //是否显示行间隔色
                cache: true,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                pagination: true,                   //是否显示分页（*）
                sortable: false,                     //是否启用排序
                sortOrder: "asc",                   //排序方式
                sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                pageNumber: 1,                      //初始化加载第一页，默认第一页,并记录
                pageSize: 20,                     //每页的记录行数（*）
                pageList: [20, 30, 50, 100],       //可供选择的每页的行数（*）
                search: true,                      //是否显示表格搜索
                strictSearch: true,                //是否全匹配搜索
                showColumns: true,                  //是否显示所有的列（选择显示的列）
                showRefresh: true,                  //是否显示刷新按钮
                minimumCountColumns: 2,             //最少允许的列数
                clickToSelect: false,                //是否启用点击选中行
                uniqueId: "id",                     //每一行的唯一标识，一般为主键列
                showToggle: false,                   //是否显示详细视图和列表视图的切换按钮
                cardView: false,                    //是否显示详细视图
                detailView: true,                  //是否显示父子表
                //格式化详情
                detailFormatter: function (index, row) {
                    var html = [];
                    $.each(row, function (key, value) {
                        if (key === 'info') {
                            //替换标签
                            value = value.replace(/&/g, "&amp;");
                            value = value.replace(/</g, "&lt;");
                            value = value.replace(/>/g, "&gt;");
                            value = value.replace(/"/g, "&quot;");
                            //替换所有的换行符
                            value = value.replace(/\r\n/g, "<br>");
                            value = value.replace(/\n/g, "<br>");
                            //替换所有的空格
                            value = value.replace(/\s/g, "&nbsp;");
                            html.push('<span>' + value + '</span>');
                        }
                    });
                    return html.join('');
                },
                queryParamsType: 'limit',
                //请求服务数据时所传参数
                queryParams:
                    function (params) {
                        return {
                            archive_id: "{{ archive_config.id }}",
                            limit: params.limit,
                            offset: params.offset,
                            search: params.search
                        }
                    },
                locale: 'zh-CN',                    //本地化
                columns: [{
                    title: '归档命令',
                    field: 'cmd',
                    visible: false // 默认不显示
                }, {
                    title: '归档模式',
                    field: 'mode'
                }, {
                    title: '归档条件',
                    field: 'condition'
                }, {
                    title: '源数据',
                    field: 'no_delete',
                    formatter: function (value, row, index) {
                        if (String(value) === 'true') {
                            return '保留'
                        } else {
                            return '删除'
                        }
                    }
                }, {
                    title: '状态',
                    field: 'success',
                    formatter: function (value, row, index) {
                        if (String(value) === 'true') {
                            return "<span class=\"label label-success\">" + '成功' + "</span>"
                        } else {
                            return "<span class=\"label label-danger\">" + '失败' + "</span>"
                        }
                    }
                }, {
                    title: '查询',
                    field: 'select_cnt'
                }, {
                    title: '插入',
                    field: 'insert_cnt'
                }, {
                    title: '删除',
                    field: 'delete_cnt'
                }, {
                    title: '开始时间',
                    field: 'start_time'
                }, {
                    title: '结束时间',
                    field: 'end_time'
                }, {
                    title: '统计日志',
                    field: 'statistics',
                    visible: false // 默认不显示
                }],
                onLoadSuccess: function () {
                },
                onLoadError: onLoadErrorCallback,
            });
        }

        // 按钮禁用
        function loading(obj) {
            $(obj).button('loading').delay(2500).queue(function () {
                $(obj).button('reset');
                $(obj).dequeue();
            });
        }

        // 校验备注
        $("#btnReject").click(function () {
            //获取form对象，判断输入，通过则提交
            $("#audit_remark").val($("#remark").val());
            var formCancel = $("#form-cancel");
            if ($("#audit_remark").val()) {
                $(this).button('loading').delay(2500).queue(function () {
                    $(this).button('reset');
                    $(this).dequeue();
                });
                formCancel.submit();
            } else {
                alert('请填写审核备注')
            }
        })
    </script>
    <!--TAB控制 -->
    <script>
        //tab切换,保留当前激活的标签id
        $(function () {
            $("#nav-tabs").on('shown.bs.tab', "li", function (e) {
                var active_li_id = $(e.target).parents().attr('id');
                sessionStorage.setItem('archive_active_li_id', active_li_id);
                //当前激活的标签id
                if (active_li_id === 'detail_tab') {
                } else if (active_li_id === 'logs_tab') {
                    get_logs();
                }
            });
        });
    </script>

{% endblock %}
