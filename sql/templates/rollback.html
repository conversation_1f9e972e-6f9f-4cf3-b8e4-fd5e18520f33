{% extends "base.html" %}

{% block content %}
    <!-- 自定义操作按钮-->
    <div id="toolbar" class="btn-group right">
        <a type='button' id="btnSubmitRollback" class="btn btn-warning" href="/editsql/">提交回滚请求</a>
    </div>
    <table id="tb-rollback" data-toggle="table" class="table table-condensed"
           style="table-layout:inherit;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;"></table>
{% endblock content %}

{% block js %}
    {% load static %}
    <script src="{% static 'bootstrap-table/js/bootstrap-table-export.min.js' %}"></script>
    <script src="{% static 'bootstrap-table/js/tableExport.min.js' %}"></script>
    <script>
        $('#tb-rollback').bootstrapTable('destroy').bootstrapTable({
            escape: true,
            method: 'get',
            contentType: "application/x-www-form-urlencoded",
            url: "/sqlworkflow/backup_sql/",
            striped: true,                      //是否显示行间隔色
            cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
            pagination: true,                   //是否显示分页（*）
            sortable: true,                     //是否启用排序
            sidePagination: "client",           //分页方式：client客户端分页，server服务端分页（*）
            pageNumber: 1,                      //初始化加载第一页，默认第一页,并记录
            pageSize: 20,                       //每页的记录行数（*）
            pageList: [10, 30, 50, 100, 500],        //可供选择的每页的行数（*）
            showExport: true,                   //是否显示导出按钮
            exportOptions: {
                ignoreColumn: [0],  //忽略某些列的索引数组
            },
            search: true,                       //是否显示表格搜索
            strictSearch: false,                //是否全匹配搜索
            showColumns: true,                  //是否显示所有的列（选择显示的列）
            showRefresh: true,                  //是否显示刷新按钮
            minimumCountColumns: 1,             //最少允许的列数
            clickToSelect: false,                //是否启用点击选中行
            uniqueId: "id",                     //每一行的唯一标识，一般为主键列
            showToggle: false,                   //是否显示详细视图和列表视图的切换按钮
            cardView: false,                    //是否显示详细视图
            toolbar: "#toolbar",               //指明自定义的toolbar
            detailView: true,                  //是否显示父子表
            //格式化详情
            detailFormatter: function (index, row) {
                var html = [];
                $.each(row, function (index, key) {
                        if (index === 1) {
                            //let sql = window.sqlFormatter.format(key);
                            let sql = key;
                            //替换标签
                            sql = sql.replace(/&/g, "&amp;");
                            sql = sql.replace(/</g, "&lt;");
                            sql = sql.replace(/>/g, "&gt;");
                            sql = sql.replace(/"/g, "&quot;");
                            //替换所有的换行符
                            sql = sql.replace(/\r\n/g, "<br>");
                            sql = sql.replace(/\n/g, "<br>");
                            //替换所有的空格
                            sql = sql.replace(/\s/g, "&nbsp;");
                            html.push('<span>' + sql + '</span>');
                        }
                    }
                );
                return html.join('');
            },
            queryParamsType: 'limit',
            //请求服务数据时所传参数
            queryParams:
                function (params) {
                    return {
                        workflow_id: "{{ workflow_detail.id }}",
                    }
                },
            locale: 'zh-CN',                    //本地化
            columns: [{
                title: '执行语句',
                field: 0,
                formatter: function (value, row, index) {
                    if (value.length > 50) {
                        return value.substr(0, 50) + '...';
                    } else {
                        return value
                    }
                }
            }, {
                title: '完整执行语句',
                field: 0,
                visible: false
            }, {
                title: '回滚语句',
                field: 1,
                formatter: function (value, row, index) {
                    var sql=row[1];
                    if (sql.length > 80) {
                        return sql.substr(0, 80) + '...';
                    } else {
                        return sql
                    }
                }
            }, {
                title: '完整回滚语句',
                field: 1,
                visible: false // 默认不显示
            }],
            onLoadSuccess: function (data) {
                // 设置回滚语句，供快速提交
                if (data.status !== 0) {
                    alert("数据加载失败！" + data.msg);
                } else {
                    let backup_sql = '';
                    for (let sql of data.rows) {
                        backup_sql += sql[1] + '\n'
                    }
                    sessionStorage.setItem('editSqlContent', backup_sql);
                }
            },
            onLoadError: onLoadErrorCallback,
        });
        $(document).ready(function () {
            var isRollback = window.location.pathname.indexOf("rollback");
            if (isRollback != -1) {
                $("#btnSubmitRollback").click(function () {
                    $(this).button('loading').delay(2500).queue(function () {
                        $(this).button('reset');
                        $(this).dequeue();
                    });
                    var editWorkflowNname = "{{ rollback_workflow_name }}";
                    var editGroup = "{{ workflow_detail.group_name }}";
                    var editClustername = "{{ workflow_detail.instance.instance_name }}";
                    var editDbname = "{{ workflow_detail.db_name }}";
                    var editIsbackup = "{{ workflow_detail.is_backup }}";
                    sessionStorage.removeItem('editWorkflowDetailId');
                    sessionStorage.setItem('editWorkflowNname', editWorkflowNname);
                    sessionStorage.setItem('editGroup', editGroup);
                    sessionStorage.setItem('editClustername', editClustername);
                    sessionStorage.setItem('editDbname', editDbname);
                    sessionStorage.setItem('editIsbackup', editIsbackup);
                });
            }
        });
    </script>
{% endblock %}
