{% extends "base.html" %}
{% load format_tags %}
{% block title %}{{ workflow_detail.title }}-{{ block.super }}{% endblock %}
{% block content %}
    <h4 style="display: inline;">工单名称：<span>{{ workflow_detail.title }}</span></h4>
    &nbsp;&nbsp;&nbsp;
    <!--只允许发起人提交其他实例-->
    {% if user.username == workflow_detail.engineer %}
        <a type='button' id="btnSubmitOtherCluster" class='btn btn-warning' href="/submitotherinstance/">上线其他实例</a>
    {% endif %}
    <input type="hidden" id="sqlMaxRowNumber" value="{{ rows|length }}">
    <input type="hidden" id="editSqlContent" value="{{ workflow_detail.sql_content }}"/>
    <hr>
    <h4>
        审批流
    </h4>
    <h5>
    {% include "workflow_display.html" %}
    <h4>
        其他信息
    </h4>
    <table data-toggle="table" class="table table-striped table-hover"
           style="table-layout:inherit;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">
        <thead>
        <tr>
            <th>
                申请人
            </th>
            <th>
                实例
            </th>
            <th>
                权限级别
            </th>
            <th>
                数据库
            </th>
            <th>
                结果集
            </th>
            <th>
                有效时间
            </th>
            <th>
                申请时间
            </th>
            <th>
                当前状态
            </th>
            <th>
                组
            </th>
        </tr>
        </thead>
        <tbody>
        <tr class="success">
            <td>
                {{ workflow_detail.user_display }}
            </td>
            <td>
                {{ workflow_detail.instance.instance_name }}
            </td>
            <td>
                {{ workflow_detail.get_priv_type_display }}
            </td>
            <td>
                {{ workflow_detail.db_list| slice:"32" }}
            </td>
            <td>
                {{ workflow_detail.limit_num }}
            </td>
            <td>
                {{ workflow_detail.valid_date }}
            </td>
            <td>
                {{ workflow_detail.create_time }}
            </td>
            <td>
                {% if workflow_detail.status == 0 %}
                    <b style="color: red">待审核</b>
                {% elif workflow_detail.status == 1 %}
                    <b style="color: green">审核通过</b>
                {% elif workflow_detail.status == 2 %}
                    <b style="color: red">审核不通过</b>
                {% elif workflow_detail.status == 3 %}
                    <b style="color: red">审核取消</b>
                {% endif %}
            </td>
            <td>
                {{ workflow_detail.group_name }}
            </td>
        </tr>
        </tbody>
    </table>
    <br>
    <div class="panel panel-default">
        <div class="panel-heading">
            {% if workflow_detail.priv_type == 1 %}
                数据库清单
            {% elif workflow_detail.priv_type == 2 %}
                表清单
            {% endif %}
        </div>
        <div class="panel-body">
            {% if workflow_detail.priv_type == 1 %}
                {% format_str workflow_detail.db_list %}
            {% elif workflow_detail.priv_type == 2 %}
                {% format_str workflow_detail.table_list %}
            {% endif %}
        </div>
    </div>
    <!--最后操作信息-->
    {% if last_operation_info %}
        <table data-toggle="table" class="table table-striped table-hover">
            <thead>
            <tr>
                <th>
                    操作信息
                </th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>
                    {{ last_operation_info }}
                </td>
            </tr>
            </tbody>
        </table>
        <br>
    {% endif %}
    {% if workflow_detail.status == 0 %}
        {% if is_can_review %}
            <textarea id="remark" name="remark" class="form-control" data-name="审核备注"
                      placeholder="请填写审核备注" rows=3></textarea>
            <br>
            <form action="/query/privaudit/" method="post" style="display:inline-block;">
                {% csrf_token %}
                <input type="hidden" name="apply_id" value="{{ workflow_detail.apply_id }}">
                <input type="hidden" id="audit_status" name="audit_status" value="1">
                <input type="submit" id="btnPass" onclick="loading(this)" class="btn btn-success" value="审核通过"/>
            </form>

            <form id="form-cancel" action="/query/privaudit/" method="post" style="display:inline-block;">
                {% csrf_token %}
                <input type="hidden" name="apply_id" value="{{ workflow_detail.apply_id }}">
                <input type="hidden" id="audit_status" name="audit_status" value="2">
                <input type="hidden" id="audit_remark" name="audit_remark" value="">
                <input type="button" id="btnReject" class="btn btn-default" value="终止流程"/>
            </form>
        {% endif %}
    {% endif %}
{% endblock content %}

{% block js %}
    <script>
        // 按钮禁用
        function loading(obj) {
            $(obj).button('loading').delay(2500).queue(function () {
                $(obj).button('reset');
                $(obj).dequeue();
            });
        }

        // 校验备注
        $("#btnReject").click(function () {
            //获取form对象，判断输入，通过则提交
            $("#audit_remark").val($("#remark").val());
            var formCancel = $("#form-cancel");
            if ($("#audit_remark").val()) {
                $(this).button('loading').delay(2500).queue(function () {
                    $(this).button('reset');
                    $(this).dequeue();
                });
                formCancel.submit();
            } else {
                alert('请填写审核备注')
            }
        })
    </script>
{% endblock %}
