# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
# 更改翻译文件后务必使用 python manage.py compilemessages 编译翻译文件
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-02-14 19:47+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
#: .\sql\models.py:111
msgid "workflow_finish"
msgstr "Workflow finished"

#: .\sql\models.py:112
msgid "workflow_abort"
msgstr "Aborted"

#: .\sql\models.py:113
msgid "workflow_manreviewing"
msgstr "Reviewing"

#: .\sql\models.py:114
msgid "workflow_review_pass"
msgstr "Review passed"

#: .\sql\models.py:115
msgid "workflow_timingtask"
msgstr "Scheduled"

msgid "workflow_queuing"
msgstr "Queuing"

#: .\sql\models.py:116
msgid "workflow_executing"
msgstr "Executing"

#: .\sql\models.py:117
msgid "workflow_autoreviewwrong"
msgstr "Rejected"

#: .\sql\models.py:118
msgid "workflow_exception"
msgstr "Exceptions during execution"
