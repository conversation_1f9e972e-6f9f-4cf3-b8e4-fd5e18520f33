#!/usr/bin/env python
# coding: utf-8

"""
飞书 OAuth 登录功能测试脚本
使用方法：
1. 配置飞书应用信息
2. 运行脚本测试各个功能模块
"""

import os
import sys
import django

# 设置 Django 环境
sys.path.append('/Users/<USER>/Desktop/Code/Archery')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archery.settings')
django.setup()

from common.utils.feishu_oauth import FeishuOAuth
from common.authenticate.feishu_auth import FeishuAuthenticationBackend
from common.config import SysConfig


def test_feishu_oauth_config():
    """测试飞书 OAuth 配置"""
    print("=== 测试飞书 OAuth 配置 ===")
    
    # 设置测试配置
    sys_config = SysConfig()
    sys_config.set("feishu_btn_name", "以飞书登录")
    sys_config.set("feishu_oauth_app_id", "cli_test_app_id")
    sys_config.set("feishu_oauth_app_secret", "test_app_secret")
    sys_config.set("feishu_oauth_redirect_uri", "http://localhost:8000/feishu/callback/")
    
    print("✓ 飞书 OAuth 配置已设置")
    
    # 测试配置读取
    feishu_oauth = FeishuOAuth()
    print(f"App ID: {feishu_oauth.app_id}")
    print(f"Redirect URI: {feishu_oauth.redirect_uri}")
    
    # 测试授权URL生成
    auth_url = feishu_oauth.get_authorization_url(state="test_state")
    print(f"授权URL: {auth_url}")
    
    return True


def test_feishu_auth_backend():
    """测试飞书认证后端"""
    print("\n=== 测试飞书认证后端 ===")
    
    backend = FeishuAuthenticationBackend()
    print("✓ 飞书认证后端创建成功")
    
    # 测试无效授权码
    user = backend.authenticate(None, code=None)
    assert user is None, "无效授权码应该返回 None"
    print("✓ 无效授权码测试通过")
    
    return True


def test_department_sync():
    """测试部门同步功能"""
    print("\n=== 测试部门同步功能 ===")
    
    from django.contrib.auth.models import Group
    
    # 创建测试组
    test_group_name = "飞书-技术部-后端组-Python组"
    group, created = Group.objects.get_or_create(name=test_group_name)
    
    if created:
        print(f"✓ 创建测试组: {test_group_name}")
    else:
        print(f"✓ 测试组已存在: {test_group_name}")
    
    # 清理测试数据
    group.delete()
    print("✓ 清理测试数据完成")
    
    return True


def test_user_model():
    """测试用户模型"""
    print("\n=== 测试用户模型 ===")
    
    from sql.models import Users
    
    # 检查用户模型是否有飞书字段
    user_fields = [field.name for field in Users._meta.fields]
    assert 'feishu_open_id' in user_fields, "用户模型缺少 feishu_open_id 字段"
    print("✓ 用户模型包含飞书字段")
    
    return True


def main():
    """主测试函数"""
    print("开始测试飞书 OAuth 登录功能...\n")
    
    tests = [
        test_feishu_oauth_config,
        test_feishu_auth_backend,
        test_department_sync,
        test_user_model,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✓ {test.__name__} 测试通过")
            else:
                failed += 1
                print(f"✗ {test.__name__} 测试失败")
        except Exception as e:
            failed += 1
            print(f"✗ {test.__name__} 测试异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 所有测试通过！飞书 OAuth 登录功能基础测试完成。")
        print("\n接下来需要：")
        print("1. 在飞书开放平台创建应用")
        print("2. 配置正确的 App ID、App Secret 和回调地址")
        print("3. 启用 ENABLE_FEISHU 环境变量")
        print("4. 重启 Archery 服务")
        print("5. 在登录页面测试飞书登录")
    else:
        print(f"\n❌ 有 {failed} 个测试失败，请检查相关配置。")


if __name__ == "__main__":
    main()
