-- goInception的动态参数配置模板
insert into param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES
('goinception','check_autoincrement_datatype','false',1,'true,false','当建表时自增列的类型不为int或者bigint时报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_autoincrement_init_value','false',1,'true,false','当建表时自增列的值指定的不为1，则报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_autoincrement_name','false',1,'true,false','建表时，如果指定的自增列的名字不为ID，则报错，说明是有意义的，给提示','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_column_comment','false',1,'true,false','建表时，列没有注释时报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_column_default_value','false',1,'true,false','检查在建表、修改列、新增列时，新的列属性是不是要有默认值','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_column_type_change','true',1,'true,false','检查字段类型变更','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_dml_limit','false',1,'true,false','在DML语句中使用了LIMIT时，是不是要报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_dml_orderby','false',1,'true,false','在DML语句中使用了Order By时，是不是要报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_dml_where','false',1,'true,false','在DML语句中没有WHERE条件时，是不是要报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_identifier','false',1,'true,false','检查标识符是否正确,规则是[a-z,A-Z,0-9,_]','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_index_prefix','false',1,'true,false','是不是要检查索引名字前缀为\"idx_\"，检查唯一索引前缀是不是\"uniq_\"','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_insert_field','false',1,'true,false','是不是要检查插入语句中的列链表的存在性','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_primary_key','false',1,'true,false','建表时，如果没有主键，则报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_table_comment','false',1,'true,false','建表时，表没有注释时报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_timestamp_count','false',1,'true,false','配置是否检查current_timestamp数量','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','check_timestamp_default','false',1,'true,false','建表时，如果没有为timestamp类型指定默认值，则报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_autoincrement_unsigned','false',1,'true,false','自增列是不是要为无符号型','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_blob_type','false',1,'true,false','检查是不是支持BLOB字段，包括建表、修改列、新增列操作','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_column_charset','false',1,'true,false','允许列自己设置字符集','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_drop_database','false',1,'true,false','是否允许删除数据库','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_drop_table','false',1,'true,false','是否允许删除表','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_enum_set_bit','false',1,'true,false','是不是支持enum,set,bit数据类型','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_fingerprint','false',1,'true,false','sql指纹功能。dml语句较多相似时，可大幅优化审核速度','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_foreign_key','false',1,'true,false','是不是支持外键','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_identifer_keyword','false',1,'true,false','检查在SQL语句中，是不是有标识符被写成MySQL的关键字，默认值为报警。','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_json_type','false',1,'true,false','设置是否允许json类型字段，包括建表、修改列、新增列操作','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_not_innodb','false',1,'true,false','建表指定的存储引擎不为Innodb，不报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_nullable','false',1,'true,false','创建或者新增列时如果列为NULL，是不是报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_null_index_name','false',1,'true,false','创建索引时是否允许空索引名','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_orderby_rand','false',1,'true,false','order by rand时是不是报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_partition_table','false',1,'true,false','是不是支持分区表','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_pk_columns_only_int','false',1,'true,false','是否强制主键列必须是int','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_select_star','false',1,'true,false','Select*时是不是要报错','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_set_charset','false',1,'true,false','是否允许指定表和数据库的字符集','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','enable_set_collation','false',1,'true,false','是否允许指定表和数据库的排序规则','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','general_log','false',1,'true,false','是否记录全量日志','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','lang','en,-US',1,'en-US,zh-CN','返回的信息使用语言,可选值`en-US`,`zh-CN`','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','max_char_length','0',1,'int','最大char长度,当超出时警告转换为varchar类型','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','max_insert_rows','0',1,'int','设置insert values允许的最大行数.0为不限制','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','max_key_parts','3',1,'int','一个索引最多可指定的列数','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','max_keys','3',1,'int','单表允许的最大索引数','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','max_primary_key_parts','3',1,'int','主键最多可指定的列数','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','max_update_rows','5000',1,'int','当update/delete预估受影响行数超出设置值时警告','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','merge_alter_table','false',1,'true,false','在多个改同一个表的语句出现是，报错，提示合成一个','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','must_have_columns','\"\"',1,'string','用以指定建表时必须创建的列。多个列时以逗号分隔(`格式: 列名 [列类型,可选]`)','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','sql_safe_updates','-1',1,'-1,0,1','安全更新.-1表示不做操作,基于远端数据库,0表示关闭安全更新,1表示开启安全更新','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','support_charset','utf8,utf8mb4',1,'string','支持的字符集,多个时以逗号分隔','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','support_collation','\"\"',1,'string','支持的排序规则,多个时以逗号分隔','2019-05-27 15:12:59.000000','2019-05-27 15:12:59.000000'),
('goinception','osc_on','false',1,'bool','OSC开关','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_alter_foreign_keys_method','none',1,'string','对应OSC参数alter-foreign-keys-method','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_bin_dir','/usr/local/bin',1,'string','pt-online-schema-change脚本的位置','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_check_alter','true',1,'bool','对应参数--[no]check-alter','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_check_interval','5',1,'int','对应参数--check-interval，意义是Sleep time between checks for --max-lag.','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_check_replication_filters','true',1,'bool','对应参数--[no]check-replication-filters','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_chunk_size','1000',1,'int','对应参数--chunk-size','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_chunk_size_limit','4',1,'int','对应参数--chunk-size-limit','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_chunk_time','1',1,'int','对应参数--chunk-time','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_critical_thread_connected','1000',1,'int','对应参数--critical-load中的thread_connected部分','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_critical_thread_running','80',1,'int','对应参数--critical-load中的thread_running部分','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_drop_new_table','true',1,'bool','对应参数--[no]drop-new-table','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_drop_old_table','true',1,'bool','对应参数--[no]drop-old-table','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_max_lag','3',1,'int','对应参数--max-lag','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_max_thread_connected','1000',1,'int','对应参数--max-load中的thread_connected部分','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_max_thread_running','80',1,'int','对应参数--max-load中的thread_running部分','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_min_table_size','16',1,'int','OSC的开关，如果设置为0，则全部ALTER语句都走OSC，如果设置为非0，则当这个表占用空间大小大于这个值时才使用OSC方式。单位为M，这个表大小的计算方式是通过语句： select (DATA_LENGTH + INDEX_LENGTH)/1024/1024 from information_schema.tables where table_schema = \"dbname\" and table_name = \"tablename\"来实现的。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_print_none','false',1,'bool','用来设置在Inception返回结果集中，对于原来OSC在执行过程的标准输出信息是不是要打印到结果集对应的错误信息列中，如果设置为1，就不打印，如果设置为0，就打印。而如果出现错误了，则都会打印','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_print_sql','false',1,'bool','对应参数--print','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','osc_recursion_method','processlist',1,'string','对应参数recursion_method','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_on','false',1,'bool','gh-ost开关','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_aliyun_rds','false',1,'bool','阿里云rds数据库标志','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_allow_master_master','false',1,'bool','允许gh-ost运行在双主复制架构中，一般与-assume-master-host参数一起使用','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_allow_nullable_unique_key','false',1,'bool','允许gh-ost在数据迁移(migrate)依赖的唯一键可以为NULL，默认为不允许为NULL的唯一键。如果数据迁移(migrate)依赖的唯一键允许NULL值，则可能造成数据不正确，请谨慎使用。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_allow_on_master','true',1,'bool','允许gh-ost直接运行在主库上。默认gh-ost连接的`主库`。`(暂未添加从库地址的配置)`','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_approve_renamed_columns','true',1,'bool','如果支持修改列名,则需设置此参数为`true`,否则gh-ost不会执行。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_assume_master_host','\"\"',1,'string','为gh-ost指定一个主库，格式为\"ip:port\"或者\"hostname:port\"。默认推荐gh-ost连接从库。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_assume_rbr','true',1,'bool','确认gh-ost连接的数据库实例的binlog_format=ROW的情况下，可以指定-assume-rbr，这样可以禁止从库上运行stop slave,start slave,执行gh-ost用户也不需要SUPER权限。`为避免影响生产数据库，此参数建议置为true`','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_chunk_size','1000',1,'int','在每次迭代中处理的行数量(允许范围：100-100000)，默认值为1000。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_concurrent_rowcount','true',1,'bool','该参数如果为True(默认值)，则进行row-copy之后，估算统计行数(使用explain select count(*)方式)，并调整ETA时间，否则，gh-ost首先预估统计行数，然后开始row-copy。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_critical_load_hibernate_seconds','0',1,'int','负载达到critical-load时，gh-ost在指定的时间内进入休眠状态。 它不会读/写任何来自任何服务器的任何内容。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_critical_load_interval_millis','0',1,'int','当值为0时，当达到-critical-load，gh-ost立即退出。当值不为0时，当达到-critical-load，gh-ost会在-critical-load-interval-millis秒数后，再次进行检查，再次检查依旧达到-critical-load，gh-ost将会退出。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_cut_over','atomic',1,'string','选择cut-over类型:atomic/two-step，atomic(默认)类型的cut-over是github的算法，two-step采用的是facebook-OSC的算法。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_cut_over_exponential_backoff','false',1,'bool','Wait exponentially longer intervals between failed cut-over attempts. Wait intervals obey a maximum configurable with \'exponential-backoff-max-interval\').','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_cut_over_lock_timeout_seconds','3',1,'int','gh-ost在cut-over阶段最大的锁等待时间，当锁超时时，gh-ost的cut-over将重试。(默认值：3)','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_default_retries','60',1,'int','各种操作在panick前重试次数。(默认为60)','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_discard_foreign_keys','false',1,'bool','该参数针对一个有外键的表，在gh-ost创建ghost表时，并不会为ghost表创建外键。该参数很适合用于删除外键，除此之外，请谨慎使用。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_dml_batch_size','10',1,'int','在单个事务中应用DML事件的批量大小（范围1-100）（默认值为10）','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_exact_rowcount','false',1,'bool','准确统计表行数(使用select count(*)的方式)，得到更准确的预估时间。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_exponential_backoff_max_interval','64',1,'int','Maximum number of seconds to wait between attempts when performing various operations with exponential backoff. (default 64)','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_force_named_cut_over','false',1,'bool','When true, the ‘unpostpone|cut-over’ interactive command must name the migrated table。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_force_table_names','\"\"',1,'string','table name prefix to be used on the temporary tables','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_gcp','false',1,'bool','google云平台支持','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_heartbeat_interval_millis','500',1,'int','gh-ost心跳频率值，默认为500。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_initially_drop_ghost_table','false',1,'bool','gh-ost操作之前，检查并删除已经存在的ghost表。该参数不建议使用，请手动处理原来存在的ghost表。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_initially_drop_old_table','false',1,'bool','gh-ost操作之前，检查并删除已经存在的旧表。该参数不建议使用，请手动处理原来存在的ghost表。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_initially_drop_socket_file','false',1,'bool','gh-ost强制删除已经存在的socket文件。该参数不建议使用，可能会删除一个正在运行的gh-ost程序，导致DDL失败。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_max_lag_millis','1500',1,'int','主从复制最大延迟时间，当主从复制延迟时间超过该值后，gh-ost将采取节流(throttle)措施，默认值：1500s。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_nice_ratio','0',1,'float','每次chunk时间段的休眠时间，范围[0.0...100.0]。e.g:0：每个chunk时间段不休眠，即一个chunk接着一个chunk执行；1：每row-copy 1毫秒，则另外休眠1毫秒；0.7：每row-copy 10毫秒，则另外休眠7毫秒。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_ok_to_drop_table','true',1,'bool','gh-ost操作结束后，删除旧表，默认状态是`删除旧表`。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_postpone_cut_over_flag_file','\"\"',1,'string','当这个文件存在的时候，gh-ost的cut-over阶段将会被推迟，直到该文件被删除。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_replication_lag_query','\"\"',1,'string','检查主从复制延迟的SQL语句，默认gh-ost通过show slave status获取Seconds_behind_master作为主从延迟时间依据。如果使用pt-heartbeat工具，检查主从复制延迟的SQL语句类似于:`SELECT ROUND(UNIX_TIMESTAMP() - MAX(UNIX_TIMESTAMP(ts))) AS delay FROM my_schema.heartbeat`;','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_skip_foreign_key_checks','true',1,'bool','跳过外键检查,默认为`true`','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_throttle_additional_flag_file','\"\"',1,'string','当该文件被创建后，gh-ost操作立即停止。该参数可以用在多个gh-ost同时操作的时候，创建一个文件，让所有的gh-ost操作停止，或者删除这个文件，让所有的gh-ost操作恢复。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_throttle_control_replicas','\"\"',1,'string','列出所有需要被检查主从复制延迟的从库。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_throttle_flag_file','\"\"',1,'string','当该文件被创建后，gh-ost操作立即停止。该参数适合控制单个gh-ost操作。-throttle-additional-flag-file string适合控制多个gh-ost操作。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_throttle_http','\"\"',1,'string','The --throttle-http flag allows for throttling via HTTP. Every 100ms gh-ost issues a HEAD request to the provided URL. If the response status code is not 200 throttling will kick in until a 200 response status code is returned.','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_throttle_query','\"\"',1,'string','节流查询。每秒钟执行一次。当返回值=0时不需要节流，当返回值>0时，需要执行节流操作。该查询会在数据迁移(migrated)服务器上操作，所以请确保该查询是轻量级的。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_timestamp_old_table','false',1,'bool','在旧表名中使用时间戳。 这会使旧表名称具有唯一且无冲突的交叉迁移','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000'),
('goinception','ghost_tungsten','false',1,'bool','告诉gh-ost你正在运行的是一个tungsten-replication拓扑结构。','2019-05-27 15:25:11.000000','2019-05-27 15:25:11.000000');
