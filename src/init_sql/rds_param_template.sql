-- 数据来源阿里云RDS MySQL5.7实例参数设置
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'automatic_sp_privileges', 'ON', 1, '[ON|OFF]', 'When this variable has a value of 1 (the default), the server automatically grants the EXECUTE and ALTER ROUTINE privileges to the creator of a stored routine, if the user cannot already execute and alter or drop the routine. (The ALTER ROUTINE privilege is required to drop the routine.) The server also automatically drops those privileges from the creator when the routine is dropped. If automatic_sp_privileges is 0, the server does not automatically add or drop these privileges.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'auto_increment_increment', '1', 1, '[1-65535]', 'Auto-increment columns are incremented by this', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'auto_increment_offset', '1', 1, '[1-65535]', 'Offset added to Auto-increment columns. Used when auto-increment-increment != 1', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'avoid_temporal_upgrade', 'OFF', 1, '[ON|OFF]', 'This variable controls whether ALTER TABLE implicitly upgrades temporal columns found to be in pre-5.6.4 format (TIME, DATETIME, and TIMESTAMP columns without support for fractional seconds precision). Upgrading such columns requires a table rebuild, which prevents any use of fast alterations that might otherwise apply to the operation to be performed.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'back_log', '3000', 0, '[0-65535]', 'The number of outstanding connection requests MySQL can have. This comes into play when the main MySQL thread gets very many connection requests in a very short time', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'binlog_cache_size', '2097152', 1, '[4096-16777216]', 'The size of the transactional cache for updates to transactional engines for the binary log. If you often use transactions containing many statements, you can increase this to get more performance', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'binlog_checksum', 'CRC32', 0, '[CRC32|NONE]', 'Type of BINLOG_CHECKSUM_ALG. Include checksum for log events in the binary log. Possible values are NONE and CRC32; default is CRC32.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'binlog_order_commits', 'ON', 1, '[ON|OFF]', 'When this variable is enabled on a master (the default), transactions are externalized in the same order as they are written to the binary log. If disabled, transactions may be committed in parallel. In some cases, disabling this variable might produce a performance increment.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'binlog_rows_query_log_events', 'OFF', 1, '[ON|OFF]', 'The binlog_rows_query_log_events system variable affects row-based logging only. When enabled, it causes the MySQL Server to write informational log events such as row query log events into its binary log. This information can be used for debugging and related purposes.such as obtaining the original query issued on the master when it cannot be reconstructed from the row updates.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'binlog_row_image', 'full', 1, '[full|minimal]', 'Controls whether rows should be logged in ''FULL'', ''NOBLOB'' or ''MINIMAL'' formats. ''FULL'', means that all columns in the before and after image are logged. ''NOBLOB'', means that mysqld avoids logging blob columns whenever possible (eg, blob column was not changed or is not part of primary key). ''MINIMAL'', means that a PK equivalent (PK columns or full row if there is no PK in the table) is logged in the before image, and only changed columns are logged in the after image. (Default: FULL).', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'binlog_stmt_cache_size', '32768', 1, '[4096-16777216]', 'The size of the statement cache for updates to non-transactional engines for the binary log. If you often use statements updating a great number of rows, you can increase this to get more performance', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'block_encryption_mode', '"aes-128-ecb"', 1, '["aes-128-ecb"|"aes-192-ecb"|"aes-256-ecb"|"aes-128-cbc"|"aes-192-cbc"|"aes-256-cbc"]', 'This variable controls the block encryption mode for block-based algorithms such as AES. It affects encryption for AES_ENCRYPT() and AES_DECRYPT().', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'bulk_insert_buffer_size', '4194304', 1, '[0-4294967295]', 'MyISAM uses a special tree-like cache to make bulk inserts faster for INSERT ... SELECT, INSERT ... VALUES (...), (...), ..., and LOAD DATA INFILE when adding data to nonempty tables. This variable limits the size of the cache tree in bytes per thread. Setting it to 0 disables this optimization. The default value is 8MB.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'character_set_filesystem', 'binary', 1, '[utf8|latin1|gbk|binary]', 'The file system character set. This variable is used to interpret string literals that refer to file names, such as in the LOAD DATA INFILE and SELECT ... INTO OUTFILE statements and the LOAD_FILE() function. Such file names are converted from character_set_client to character_set_filesystem before the file opening attempt occurs. The default value is binary, which means that no conversion occurs. For systems on which multibyte file names are permitted, a different value may be more appropriate. For example, if the system represents file names using UTF-8, set character_set_filesystem to ''utf8''.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'character_set_server', 'utf8', 0, '[utf8|latin1|gbk|utf8mb4]', 'The default character set', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'concurrent_insert', '1', 1, '[0|1|2]', 'Use concurrent insert with MyISAM. Possible values are NEVER, AUTO, ALWAYS', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'connect_timeout', '10', 1, '[1-3600]', 'The number of seconds the mysqld server is waiting for a connect packet before responding with ''Bad handshake''', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'default_storage_engine', 'InnoDB', 0, '[InnoDB|innodb]', 'The default storage engine for new tables', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'default_time_zone', 'SYSTEM', 0, '[SYSTEM|-12:00|-11:00|-10:00|-9:00|-8:00|-7:00|-6:00|-5:00|-4:00|-3:00|-2:00|-1:00|\\+0:00|\\+1:00|\\+2:00|\\+3:00|\\+4:00|\\+5:00|\\+5:30|\\+6:00|\\+6:30|\\+7:00|\\+8:00|\\+9:00|\\+10:00|\\+11:00|\\+12:00|\\+13:00]', 'The default time zone for the database', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'default_week_format', '0', 1, '[0-7]', 'The default week format used by WEEK() functions', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'delayed_insert_limit', '100', 1, '[1-4294967295]', 'After inserting delayed_insert_limit rows, the INSERT DELAYED handler will check if there are any SELECT statements pending. If so, it allows these to execute before continuing. This variable is deprecated along with INSERT DELAYED.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'delayed_insert_timeout', '300', 1, '[1-3600]', 'How long a INSERT DELAYED thread should wait for INSERT statements before terminating. This variable is deprecated along with INSERT DELAYED.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'delayed_queue_size', '1000', 1, '[1-4294967295]', 'What size queue (in rows) should be allocated for handling INSERT DELAYED. If the queue becomes full, any client that does INSERT DELAYED will wait until there is room in the queue again. This variable is deprecated along with INSERT DELAYED.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'delay_key_write', 'ON', 1, '[ON|OFF|ALL]', 'Type of DELAY_KEY_WRITE', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'disconnect_on_expired_password', 'ON', 0, '[ON|OFF]', 'This variable controls how the server handles clients with expired passwords', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'div_precision_increment', '4', 1, '[0-30]', 'Precision of the result of ''/'' operator will be increased on that value', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'end_markers_in_json', 'OFF', 1, '[ON|OFF]', 'Whether optimizer JSON output should add end markers. ', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'eq_range_index_dive_limit', '10', 1, '[0-4294967295]', 'The optimizer will use existing index statistics instead of doing index dives for equality ranges if the number of equality ranges for the index is larger than or equal to this number. If set to 0, index dives are always used.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'event_scheduler', 'OFF', 1, 'ON', 'Enable the event scheduler. Possible values are ON, OFF, and DISABLED (keep the event scheduler completely deactivated, it cannot be activated run-time)', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'explicit_defaults_for_timestamp', 'OFF', 1, '[ON|OFF]', 'This system variable determines whether the server enables certain nonstandard behaviors for default values and NULL-value handling in TIMESTAMP columns.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'flush_time', '0', 1, '[0-31536000]', 'If this is set to a nonzero value, all tables are closed every flush_time seconds to free up resources and synchronize unflushed data to disk. This option is best used only on systems with minimal resources.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'ft_max_word_len', '84', 0, '[10-4294967295]', 'The maximum length of the word to be included in a MyISAM FULLTEXT index.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'ft_min_word_len', '4', 0, '[1-3600]', 'The minimum length of the word to be included in a FULLTEXT index. Note: FULLTEXT indexes must be rebuilt after changing this variable', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'ft_query_expansion_limit', '20', 0, '[0-1000]', 'Number of best matches to use for query expansion', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'group_concat_max_len', '1024', 1, '[4-1844674407370954752]', 'The maximum length of the result of function  GROUP_CONCAT()', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'host_cache_size', '644', 1, '[0-65535]', 'The size of the internal host cache (see Section ********, “DNS Lookup Optimization and the Host Cache”). Setting the size to 0 disables the host cache. Changing the cache size at runtime implicitly causes a FLUSH HOSTS operation to clear the host cache and truncate the host_cache table.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_adaptive_flushing', 'ON', 1, '[ON|OFF]', 'Specifies whether to dynamically adjust the rate of flushing dirty pages in the InnoDB buffer pool based on the workload. Adjusting the flush rate dynamically is intended to avoid bursts of I/O activity.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_adaptive_flushing_lwm', '10', 1, '[0-70]', 'Defines the low water mark representing percentage of redo log capacity at which adaptive flushing is enabled.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_adaptive_hash_index', 'ON', 1, '[ON|OFF]', 'Enable InnoDB adaptive hash index (enabled by default).  Disable with --skip-innodb-adaptive-hash-index.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_adaptive_max_sleep_delay', '150000', 1, '[1-1000000]', 'Permits InnoDB to automatically adjust the value of innodb_thread_sleep_delay up or down according to the current workload. Any nonzero value enables automated, dynamic adjustment of the innodb_thread_sleep_delay value, up to the maximum value specified in the innodb_adaptive_max_sleep_delay option. The value represents the number of microseconds. This option can be useful in busy systems, with greater than 16 InnoDB threads. (In practice, it is most valuable for MySQL systems with hundreds or thousands of simultaneous connections.)', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_autoextend_increment', '64', 1, '[1-1000]', 'The increment size (in megabytes) for extending the size of an auto-extending InnoDB system tablespace file when it becomes full. The default value is 64. ', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_autoinc_lock_mode', '1', 0, '[0|1|2]', 'The AUTOINC lock modes supported by InnoDB: 0 => Old style AUTOINC locking (for backward compatibility), 1 => New style AUTOINC locking; 2 => No AUTOINC locking (unsafe for SBR)', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_buffer_pool_dump_at_shutdown', 'ON', 1, '[ON|OFF]', 'Specifies whether to record the pages cached in the InnoDB buffer pool when the MySQL server is shut down, to shorten the warmup process at the next restart. Typically used in combination with innodb_buffer_pool_load_at_startup. The innodb_buffer_pool_dump_pct option defines the percentage of most recently used buffer pool pages to dump.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_buffer_pool_dump_pct', '25', 1, '[1-100]', 'Specifies the percentage of the most recently used pages for each buffer pool to read out and dump. The range is 1 to 100. The default value is 25. For example, if there are 4 buffer pools with 100 pages each, and innodb_buffer_pool_dump_pct is set to 25, the 25 most recently used pages from each buffer pool are dumped.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_buffer_pool_instances', '1', 0, '[1-64]', 'The number of regions that the InnoDB buffer pool is divided into. For systems with buffer pools in the multi-gigabyte range, dividing the buffer pool into separate instances can improve concurrency, by reducing contention as different threads read and write to cached pages. Each page that is stored in or read from the buffer pool is assigned to one of the buffer pool instances randomly, using a hashing function. Each buffer pool manages its own free lists, flush lists, LRUs, and all other data structures connected to a buffer pool, and is protected by its own buffer pool mutex.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_buffer_pool_load_at_startup', 'ON', 0, '[ON|OFF]', 'Specifies that, on MySQL server startup, the InnoDB buffer pool is automatically warmed up by loading the same pages it held at an earlier time. Typically used in combination with innodb_buffer_pool_dump_at_shutdown.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_change_buffering', 'all', 1, '[none|inserts|deletes|changes|purges|all]', 'Whether InnoDB performs change buffering, an optimization that delays write operations to secondary indexes so that the I/O operations can be performed sequentially. Permitted values are described in the following table.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_change_buffer_max_size', '25', 1, '[0-50]', 'Maximum size for the InnoDB change buffer, as a percentage of the total size of the buffer pool. You might increase this value for a MySQL server with heavy insert, update, and delete activity, or decrease it for a MySQL server with unchanging data used for reporting.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_checksum_algorithm', 'crc32', 1, '[innodb|crc32|none|strict_innodb|strict_crc32|strict_none]', 'Specifies how to generate and verify the checksum stored in the disk blocks of InnoDB tablespaces.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_cmp_per_index_enabled', 'OFF', 1, '[ON|OFF]', 'Enables per-index compression-related statistics in the INFORMATION_SCHEMA.INNODB_CMP_PER_INDEX table. Because these statistics can be expensive to gather, only enable this option on development, test, or slave instances during performance tuning related to InnoDB compressed tables.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_commit_concurrency', '0', 0, '[0-1000]', 'The number of threads that can commit at the same time. A value of 0 (the default) permits any number of transactions to commit simultaneously.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_compression_failure_threshold_pct', '5', 1, '[0-100]', 'Defines the compression failure rate threshold for a table, as a percentage, at which point MySQL begins adding padding within compressed pages to avoid expensive compression failures. When this threshold is passed, MySQL begins to leave additional free space within each new compressed page, dynamically adjusting the amount of free space up to the percentage of page size specified by innodb_compression_pad_pct_max. A value of zero disables the mechanism that monitors compression efficiency and dynamically adjusts the padding amount.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_compression_level', '6', 1, '[0-9]', 'Specifies the level of zlib compression to use for InnoDB compressed tables and indexes. A higher value lets you fit more data onto a storage device, at the expense of more CPU overhead during compression. A lower value lets you reduce CPU overhead when storage space is not critical, or you expect the data is not especially compressible.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_compression_pad_pct_max', '50', 1, '[0-70]', 'Specifies the maximum percentage that can be reserved as free space within each compressed page, allowing room to reorganize the data and modification log within the page when a compressed table or index is updated and the data might be recompressed. Only applies when innodb_compression_failure_threshold_pct is set to a nonzero value, and the rate of compression failures passes the cutoff point.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_concurrency_tickets', '5000', 1, '[1-4294967295]', 'Number of times a thread is allowed to enter InnoDB within the same SQL query after it has once got the ticket', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_deadlock_detect', 'ON', 1, '[ON|OFF]', 'This option is used to disable deadlock detection. On high concurrency systems, deadlock detection can cause a slowdown when numerous threads wait for the same lock. At times, it may be more efficient to disable deadlock detection and rely on the innodb_lock_wait_timeout setting for transaction rollback when a deadlock occurs.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_disable_sort_file_cache', 'ON', 1, '[ON|OFF]', 'if ON, disable page cache when innodb using sort file', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_flush_method', 'O_DIRECT', 0, '[fsync|O_DSYNC|littlesync|nosync|O_DIRECT|O_DIRECT_NO_FSYNC]', 'Defines the method used to flush data to InnoDB data files and log files, which can affect I/O throughput.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_flush_neighbors', '1', 1, '[0|1|2]', 'Specifies whether flushing a page from the InnoDB buffer pool also flushes other dirty pages in the same extent.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_flush_sync', 'ON', 1, '[ON|OFF]', 'The innodb_flush_sync parameter, which is enabled by default, causes the innodb_io_capacity setting to be ignored for bursts of I/O activity that occur at checkpoints. To adhere to the limit on InnoDB background I/O activity defined by the innodb_io_capacity setting, disable innodb_flush_sync.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_ft_cache_size', '8000000', 0, '[1600000-80000000]', 'The memory allocated, in bytes, for the InnoDB FULLTEXT search index cache, which holds a parsed document in memory while creating an InnoDB FULLTEXT index. Index inserts and updates are only committed to disk when the innodb_ft_cache_size size limit is reached. innodb_ft_cache_size defines the cache size on a per table basis.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_ft_enable_diag_print', 'OFF', 1, '[ON|OFF]', 'Whether to enable additional full-text search (FTS) diagnostic output. This option is primarily intended for advanced FTS debugging and will not be of interest to most users. Output is printed to the error log and includes information', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_ft_enable_stopword', 'ON', 1, '[ON|OFF]', 'Specifies that a set of stopwords is associated with an InnoDB FULLTEXT index at the time the index is created. If the innodb_ft_user_stopword_table option is set, the stopwords are taken from that table. Else, if the innodb_ft_server_stopword_table option is set, the stopwords are taken from that table. Otherwise, a built-in set of default stopwords is used.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_ft_max_token_size', '84', 0, '[10-84]', 'InnoDB Fulltext search maximum token size in characters', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_ft_min_token_size', '3', 0, '[0-16]', 'InnoDB Fulltext search minimum token size in characters', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_ft_num_word_optimize', '2000', 1, '[0-10000]', 'Number of words to process during each OPTIMIZE TABLE operation on an InnoDB FULLTEXT index. Because a bulk insert or update operation to a table containing a full-text search index could require substantial index maintenance to incorporate all changes, you might do a series of OPTIMIZE TABLE statements, each picking up where the last left off.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_ft_result_cache_limit', '2000000000', 1, '[1000000-4294967295]', 'The InnoDB full-text search query result cache limit (defined in bytes) per full-text search query or per thread. Intermediate and final InnoDB full-text search query results are handled in memory. Use innodb_ft_result_cache_limit to place a size limit on the full-text search query result cache to avoid excessive memory consumption in case of very large InnoDB full-text search query results (millions or hundreds of millions of rows, for example). Memory is allocated as required when a full-text search query is processed. If the result cache size limit is reached, an error is returned indicating that the query exceeds the maximum allowed memory.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_ft_sort_pll_degree', '2', 0, '[1-16]', 'Number of threads used in parallel to index and tokenize text in an InnoDB FULLTEXT index when building a search index.', '2019-04-08 01:54:41', '2019-04-08 01:54:41');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_ft_total_cache_size', '640000000', 0, '[32000000-1600000000]', 'The total memory allocated, in bytes, for the InnoDB full-text search index cache for all tables. Creating numerous tables, each with a FULLTEXT search index, could consume a significant portion of available memory. innodb_ft_total_cache_size defines a global memory limit for all full-text search indexes to help avoid excessive memory consumption. If the global limit is reached by an index operation, a forced sync is triggered.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_io_capacity', '2000', 1, '[100-4294967295]', 'The innodb_io_capacity parameter sets an upper limit on the number of I/O operations performed per second by InnoDB background tasks, such as flushing pages from the buffer pool and merging data from the change buffer.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_io_capacity_max', '4000', 1, '[100-4294967295]', 'If flushing activity falls behind, InnoDB can flush more aggressively than the limit imposed by innodb_io_capacity. innodb_io_capacity_max defines an upper limit the number of I/O operations performed per second by InnoDB background tasks in such situations.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_large_prefix', 'OFF', 1, '[ON|OFF]', 'Support large index prefix length of REC_VERSION_56_MAX_INDEX_COL_LEN (3072) bytes.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_lock_wait_timeout', '50', 1, '[1-1073741824]', 'Timeout in seconds an InnoDB transaction may wait for a lock before being rolled back. Values above 100000000 disable the timeout.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_log_checksums', 'ON', 1, '[ON|OFF]', 'Enables or disables checksums for redo log pages. innodb_log_checksums replaces innodb_log_checksum_algorithm.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_log_compressed_pages', 'OFF', 1, '[ON|OFF]', 'Specifies whether images of re-compressed pages are written to the redo log. Re-compression may occur when changes are made to compressed data.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_lru_scan_depth', '1024', 1, '[100-4294967295]', 'A parameter that influences the algorithms and heuristics for the flush operation for the InnoDB buffer pool. Primarily of interest to performance experts tuning I/O-intensive workloads. It specifies, per buffer pool instance, how far down the buffer pool LRU page list the page cleaner thread scans looking for dirty pages to flush. This is a background operation performed once per second.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_max_dirty_pages_pct', '75', 1, '[50-90]', 'Percentage of dirty pages allowed in bufferpool.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_max_dirty_pages_pct_lwm', '0', 1, '[0-75]', 'Defines a low water mark representing the percentage of dirty pages at which preflushing is enabled to control the dirty page ratio. The default of 0 disables the pre-flushing behavior entirely', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_max_purge_lag', '0', 1, '[0-4294967295]', 'Defines the maximum length of the purge queue. The default value of 0 indicates no limit (no delays).', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_max_purge_lag_delay', '0', 1, '[0-10000000]', 'Specifies the maximum delay in microseconds for the delay imposed by theinnodb_max_purge_lag configuration option. A nonzero value represents an upper limit on the delay period computed from the formula based on the value of innodb_max_purge_lag. The default of zero means that there is no upper limit imposed on the delay interval.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_max_undo_log_size', '1073741824', 1, '[10485760-18446744073709551615]', 'Defines a threshold size for undo tablespaces. If an undo tablespace exceeds the threshold, it can be marked for truncation when innodb_undo_log_truncate is enabled. The default value is 1073741824 bytes (1024 MiB).', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_monitor_disable', '', 1, 'all', 'Disables InnoDB metrics counters. Counter data may be queried using the INFORMATION_SCHEMA.INNODB_METRICS table.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_monitor_enable', '', 1, 'all', 'innodb_monitor_enable', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_old_blocks_pct', '37', 1, '[5-95]', 'Percentage of the buffer pool to reserve for ''old'' blocks.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_old_blocks_time', '1000', 1, '[0-1024]', 'Move blocks to the ''new'' end of the buffer pool if the first access was at least this many milliseconds ago. The timeout is disabled if 0.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_online_alter_log_max_size', '134217728', 1, '[134217728-2147483647]', 'Maximum modification log file size for online index creation', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_open_files', '3000', 0, '[1-8192]', 'How many files at the maximum InnoDB keeps open at the same time.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_optimize_fulltext_only', 'OFF', 1, '[ON|OFF]', 'Changes the way OPTIMIZE TABLE operates on InnoDB tables. Intended to be enabled temporarily, during maintenance operations for InnoDB tables with FULLTEXT indexes.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_page_cleaners', '1', 0, '[1-64]', 'The number of page cleaner threads that flush dirty pages from buffer pool instances. Page cleaner threads perform flush list and LRU flushing. A single page cleaner thread was introduced in MySQL 5.6 to offload buffer pool flushing work from the InnoDB master thread. In MySQL 5.7, InnoDB provides support for multiple page cleaner threads. A value of 1 maintains the pre-MySQL 5.7 configuration in which there is a single page cleaner thread. When there are multiple page cleaner threads, buffer pool flushing tasks for each buffer pool instance are dispatched to idle page cleaner threads. The innodb_page_cleaners default value was changed from 1 to 4 in MySQL 5.7. If the number of page cleaner threads exceeds the number of buffer pool instances', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_print_all_deadlocks', 'OFF', 1, '[OFF|ON]', 'Print all deadlocks to MySQL error log (off by default)', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_purge_batch_size', '300', 0, '[1-5000]', 'Number of UNDO log pages to purge in one batch from the history list.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_purge_rseg_truncate_frequency', '128', 1, '[1-128]', 'Defines the frequency with which the purge system frees rollback segments in terms of the number of times that purge is invoked. An undo tablespace cannot be truncated until its rollback segments are freed. Normally, the purge system frees rollback segments once every 128 times that purge is invoked. The default value is 128. Reducing this value increases the frequency with which the purge thread frees rollback segments.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_purge_threads', '1', 0, '[1-32]', 'Purge threads can be from 1 to 32. Default is 4.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_random_read_ahead', 'OFF', 1, '[ON|OFF]', 'Enables the random read-ahead technique for optimizing InnoDB I/O.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_read_ahead_threshold', '56', 1, '[0-64]', 'Number of pages that must be accessed sequentially for InnoDB to trigger a readahead.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_read_io_threads', '4', 0, '[1-64]', 'Number of background read I/O threads in InnoDB.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_rollback_on_timeout', 'OFF', 0, '[OFF|ON]', 'Roll back the complete transaction on lock wait timeout, for 4.x compatibility (disabled by default)', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_rollback_segments', '128', 1, '[1-128]', 'Defines the number of rollback segments used by InnoDB.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_sort_buffer_size', '1048576', 0, '[65536-67108864]', 'Specifies the size of sort buffers used to sort data during creation of an InnoDB index. The specified size defines the amount of data that is read into memory for internal sorting and then written out to disk. This process is referred to as a “run”. During the merge phase, pairs of buffers of the specified size are read in and merged. The larger the setting, the fewer runs and merges there are.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_spin_wait_delay', '30', 1, '[0-4294967295]', 'The maximum delay between polls for a spin lock. The low-level implementation of this mechanism varies depending on the combination of hardware and operating system, so the delay does not correspond to a fixed time interval. For more information, see Section 14.6.10, “Configuring Spin Lock Polling”.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_stats_auto_recalc', 'ON', 1, '[ON|OFF]', 'Causes InnoDB to automatically recalculate persistent statistics after the data in a table is changed substantially. The threshold value is 10% of the rows in the table. This setting applies to tables created when the innodb_stats_persistent option is enabled. Automatic statistics recalculation may also be configured by specifying STATS_PERSISTENT=1 in a CREATE TABLE or ALTER TABLE statement. The amount of data sampled to produce the statistics is controlled by the innodb_stats_persistent_sample_pages configuration option.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_stats_method', 'nulls_equal', 1, '[nulls_equal|nulls_unequal|nulls_ignored]', 'Specifies how InnoDB index statistics collection code should treat NULLs. Possible values are NULLS_EQUAL (default), NULLS_UNEQUAL and NULLS_IGNORED', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_stats_on_metadata', 'OFF', 1, '[ON|OFF]', 'Enable statistics gathering for metadata commands such as SHOW TABLE STATUS for tables that use transient statistics (off by default)', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_stats_persistent', 'ON', 1, '[ON|OFF]', 'Specifies whether InnoDB index statistics are persisted to disk. Otherwise, statistics may be recalculated frequently which can lead to variations in query execution plans. This setting is stored with each table when the table is created. You can set innodb_stats_persistent at the global level before creating a table, or use the STATS_PERSISTENT clause of the CREATE TABLE and ALTER TABLE statements to override the system-wide setting and configure persistent statistics for individual tables.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_stats_persistent_sample_pages', '20', 1, '[0-4294967295]', 'The number of index pages to sample when estimating cardinality and other statistics for an indexed column, such as those calculated by ANALYZE TABLE. Increasing the value improves the accuracy of index statistics, which can improve the query execution plan, at the expense of increased I/O during the execution of ANALYZE TABLE for an InnoDB table.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_stats_sample_pages', '8', 1, '[1-4294967296]', 'Deprecated, use innodb_stats_transient_sample_pages instead', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_stats_transient_sample_pages', '8', 1, '[1-4294967295]', 'The number of index pages to sample when estimating cardinality and other statistics for an indexed column, such as those calculated by ANALYZE TABLE. The default value is 8. Increasing the value improves the accuracy of index statistics, which can improve the query execution plan, at the expense of increased I/O when opening an InnoDB table or recalculating statistics.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_status_output', 'OFF', 1, '[ON|OFF]', 'Enables or disables periodic output for the standard InnoDB Monitor. Also used in combination with innodb_status_output_locks to enable or disable periodic output for the InnoDB Lock Monitor.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_status_output_locks', 'OFF', 1, '[ON|OFF]', 'Enables or disables the InnoDB Lock Monitor. When enabled, the InnoDB Lock Monitor prints additional information about locks in SHOW ENGINE INNODB STATUS output and in periodic output printed to the MySQL error log. Periodic output for the InnoDB Lock Monitor is printed as part of the standard InnoDB Monitor output. The standard InnoDB Monitor must therefore be enabled for the InnoDB Lock Monitor to print data to the MySQL error log periodically.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_strict_mode', 'OFF', 1, '[ON|OFF]', 'Use strict mode when evaluating create options.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_sync_array_size', '1', 0, '[1-1024]', 'Defines the size of the mutex/lock wait array. Increasing the value splits the internal data structure used to coordinate threads, for higher concurrency in workloads with large numbers of waiting threads. This setting must be configured when the MySQL instance is starting up, and cannot be changed afterward. Increasing the value is recommended for workloads that frequently produce a large number of waiting threads, typically greater than 768.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_sync_spin_loops', '100', 1, '[0-4294967295]', 'The number of times a thread waits for an InnoDB mutex to be freed before the thread is suspended.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_table_locks', 'ON', 1, '[ON|OFF]', 'Enable InnoDB locking in LOCK TABLES', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_thread_concurrency', '0', 1, '[0-128]', 'Helps in performance tuning in heavily concurrent environments. Sets the maximum number of threads allowed inside InnoDB. Value 0 will disable the thread throttling.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_thread_sleep_delay', '10000', 1, '[1-3600000]', 'Time of innodb thread sleeping before joining InnoDB queue (usec). Value 0 disable a sleep', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'innodb_write_io_threads', '4', 0, '[1-64]', 'Number of background write I/O threads in InnoDB.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'interactive_timeout', '7200', 1, '[10-86400]', 'The number of seconds the server waits for activity on an interactive connection before closing it', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'join_buffer_size', '262144', 1, '[128-4294967295]', 'The minimum size of the buffer that is used for plain index scans, range index scans, and joins that do not use indexes and thus perform full table scans.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'key_cache_age_threshold', '300', 1, '[100-4294967295]', 'This characterizes the number of hits a hot block has to be untouched until it is considered aged enough to be downgraded to a warm block. This specifies the percentage ratio of that number of hits to the total number of blocks in key cache', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'key_cache_block_size', '1024', 1, '[512-16384]', 'The default size of key cache blocks', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'key_cache_division_limit', '100', 1, '[1-100]', 'The minimum percentage of warm blocks in key cache', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'lc_time_names', 'en_US', 1, '[ja_JP|pt_BR|en_US]', 'This variable specifies the locale that controls the language used to display day and month names and abbreviations. This variable affects the output from the DATE_FORMAT(), DAYNAME() and MONTHNAME() functions. Locale names are POSIX-style values such as ''ja_JP'' or ''pt_BR''. The default value is ''en_US'' regardless of your system''s locale setting.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'lock_wait_timeout', '31536000', 1, '[1-1073741824]', 'This variable specifies the timeout in seconds for attempts to acquire metadata locks. The permissible values range from 1 to 31536000 (1 year). The default is 31536000.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'log_queries_not_using_indexes', 'OFF', 1, '[ON|OFF]', 'Log queries that are executed without benefit of any index to the slow log if it is open', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'log_throttle_queries_not_using_indexes', '0', 1, '[0-4294967295]', 'If log_queries_not_using_indexes is enabled, the log_throttle_queries_not_using_indexes variable limits the number of such queries per minute that can be written to the slow query log. ', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'long_query_time', '1', 1, '[0.03-31536000]', 'Log all queries that have taken more than long_query_time seconds to execute to file. The argument will be treated as a decimal value with microsecond precision', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'loose_optimizer_trace', 'enabled=off,one_line=off', 1, '.*', 'The optimizer_trace system variable has these on/off switches,enabled,one_line', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'loose_optimizer_trace_features', 'greedy_search=on,range_optimizer=on,dynamic_range=on,repeated_subselect=on', 1, '.*', 'Those features can be excluded from tracing using the optimizer_trace_features system variable', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'loose_thread_handling', '"one-thread-per-connection"', 0, 'one-thread-per-connection', 'Define threads usage for handling queries, one of one-thread-per-connection, no-threads, loaded-dynamically', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'loose_thread_pool_oversubscribe', '10', 1, '', '', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'loose_thread_pool_stall_limit', '30', 1, '', '', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'loose_validate_password_length', '8', 1, '[1-12]', 'The minimum number of characters that validate_password requires passwords to have. This variable is unavailable unless validate_password is installed.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'low_priority_updates', '0', 1, '[0|1]', 'INSERT/DELETE/UPDATE has lower priority than selects', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'master_verify_checksum', 'OFF', 1, '[ON|OFF]', 'Enabling this variable causes the master to examine checksums when reading from the binary log. master_verify_checksum is disabled by default.in this case, the master uses the event length from the binary log to verify events, so that only complete events are read from the binary log.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_allowed_packet', '1073741824', 1, '[16384-1073741824]', 'The packet message buffer is initialized to net_buffer_length bytes, but can grow up to max_allowed_packet bytes when needed. This value by default is small, to catch large (possibly incorrect) packets.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_binlog_stmt_cache_size', '18446744073709547520', 1, '[4096-18446744073709547520]', 'If nontransactional statements within a transaction require more than this many bytes of memory, the server generates an error. The minimum value is 4096. The maximum and default values are 4GB on 32-bit platforms and 16EB (exabytes) on 64-bit platforms.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_connect_errors', '100', 1, '[1-4294967295]', 'If there is more than this number of interrupted connections from a host this host will be blocked from further connections', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_error_count', '64', 1, '[0-65535]', 'The maximum number of error, warning, and information messages to be stored for display by the SHOW ERRORS and SHOW WARNINGS statements. This is the same as the number of condition areas in the diagnostics area, and thus the number of conditions that can be inspected by GET DIAGNOSTICS.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_heap_table_size', '67108864', 1, '[16384-1844674407370954752]', 'This variable sets the maximum size to which user-created MEMORY tables are permitted to grow. The value of the variable is used to calculate MEMORY table MAX_ROWS values. Setting this variable has no effect on any existing MEMORY table, unless the table is re-created with a statement such as CREATE TABLE or altered with ALTER TABLE or TRUNCATE TABLE. A server restart also sets the maximum size of existing MEMORY tables to the global max_heap_table_size value.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_join_size', '18446744073709551615', 1, '[1-18446744073709551615]', 'Do not permit statements that probably need to examine more than max_join_size rows (for single-table statements) or row combinations (for multiple-table statements) or that are likely to do more than max_join_size disk seeks. By setting this value, you can catch statements where keys are not used properly and that would probably take a long time. Set it if your users tend to perform joins that lack a WHERE clause, that take a long time, or that return millions of rows.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_length_for_sort_data', '1024', 1, '[0-838860]', 'Max number of bytes in sorted records', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_points_in_geometry', '65536', 1, '[3-1048576]', 'The maximum value of the points_per_circle argument to the ST_Buffer_Strategy() function.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_prepared_stmt_count', '16382', 1, '[0-1048576]', 'Maximum number of prepared statements in the server', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_seeks_for_key', '18446744073709500000', 1, '[1-18446744073709551615]', 'Limit the assumed maximum number of seeks when looking up rows based on a key. The MySQL optimizer assumes that no more than this number of key seeks are required when searching for matching rows in a table by scanning an index, regardless of the actual cardinality of the index. By setting this to a low value (say, 100), you can force MySQL to prefer indexes instead of table scans.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_sort_length', '1024', 1, '[4-8388608]', 'The number of bytes to use when sorting data values. The server uses only the first max_sort_length bytes of each value and ignores the rest. Consequently, values that differ only after the first max_sort_length bytes compare as equal for GROUP BY, ORDER BY, and DISTINCT operations.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_sp_recursion_depth', '0', 1, '[0-255]', 'The number of times that any given stored procedure may be called recursively. The default value for this option is 0, which completely disables recursion in stored procedures. The maximum value is 255.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'max_write_lock_count', '102400', 1, '[1-102400]', 'After this many write locks, allow some read locks to run in between', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'metadata_locks_cache_size', '1024', 0, '[1-1048576]', 'The size of the metadata locks cache. The server uses this cache to avoid creation and destruction of synchronization objects. This is particularly helpful on systems where such operations are expensive, such as Windows XP.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'min_examined_row_limit', '0', 1, '[0-4294967295]', 'Queries that examine fewer than this number of rows are not logged to the slow query log.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'myisam_sort_buffer_size', '262144', 1, '[262144-16777216]', 'The buffer that is allocated when sorting the index when doing a REPAIR or when creating indexes with CREATE INDEX or ALTER TABLE', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'mysql_native_password_proxy_users', 'OFF', 1, '[ON|OFF]', 'This variable controls whether the mysql_native_password built-in authentication plugin supports proxy users. It has no effect unless the check_proxy_users system variable is enabled. ', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'net_buffer_length', '16384', 1, '[1024-1048576]', 'Each client thread is associated with a connection buffer and result buffer. Both begin with a size given by net_buffer_length but are dynamically enlarged up to max_allowed_packet bytes as needed. The result buffer shrinks to net_buffer_length after each SQL statement.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'net_read_timeout', '30', 1, '[1-31536000]', 'Number of seconds to wait for more data from a connection before aborting the read', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'net_retry_count', '10', 1, '[1-4294967295]', 'If a read on a communication port is interrupted, retry this many times before giving up', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'net_write_timeout', '60', 1, '[1-31536000]', 'Number of seconds to wait for a block to be written to a connection before aborting the write', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'ngram_token_size', '2', 0, '[0-20]', 'Defines the n-gram token size for the n-gram full-text parser. The ngram_token_size option is read-only and can only be modified at startup. The default value is 2 (bigram). The maximum value is 10.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'old_passwords', '0', 1, '[0|2]', 'This variable controls the password hashing method used by the PASSWORD() function. It also influences password hashing performed by CREATE USER and GRANT statements that specify a password using an IDENTIFIED BY clause', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'open_files_limit', '65535', 0, '[4000-65535]', 'If this is not 0, then mysqld will use this value to reserve file descriptors to use with setrlimit(). If this value is 0 then mysqld will reserve max_connections*5 or max_connections + table_open_cache*2 (whichever is larger) number of file descriptors', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'optimizer_prune_level', '1', 1, '[0|1]', 'Controls the heuristics applied during query optimization to prune less-promising partial plans from the optimizer search space. A value of 0 disables heuristics so that the optimizer performs an exhaustive search. A value of 1 causes the optimizer to prune plans based on the number of rows retrieved by intermediate plans.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'optimizer_search_depth', '62', 1, '[0-62]', 'The maximum depth of search performed by the query optimizer. Values larger than the number of relations in a query result in better query plans, but take longer to generate an execution plan for a query. Values smaller than the number of relations in a query return an execution plan quicker, but the resulting plan may be far from being optimal. If set to 0, the system automatically picks a reasonable value.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'optimizer_trace_limit', '1', 1, '[0-4294967295]', 'The maximum number of optimizer traces to display. ', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'optimizer_trace_max_mem_size', '16384', 1, '[0-4294967295]', 'The maximum cumulative size of stored optimizer traces. ', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'optimizer_trace_offset', '-1', 1, '[0-4294967295]', 'The offset of optimizer traces to display.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'performance_schema', 'OFF', 0, '[ON|OFF]', 'Enable the performance schema.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'preload_buffer_size', '32768', 1, '[1024-1073741824]', 'The size of the buffer that is allocated when preloading indexes.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'query_alloc_block_size', '8192', 1, '[1024-16384]', 'Allocation block size for query parsing and execution', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'query_cache_limit', '1048576', 1, '[1-1048576]', 'Don''t cache results that are bigger than this', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'query_cache_min_res_unit', '1024', 1, '[512-18446744073709551608]', 'The minimum size (in bytes) for blocks allocated by the query cache. The default value is 4096 (4KB).', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'query_cache_size', '3145728', 1, '[0-104857600]', 'The memory allocated to store results from old queries', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'query_cache_type', '0', 0, '[0|1|2]', 'OFF = Don''t cache or retrieve results. ON = Cache all results except SELECT SQL_NO_CACHE ... queries. DEMAND = Cache only SELECT SQL_CACHE ... queries', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'query_cache_wlock_invalidate', 'OFF', 1, '[ON|OFF]', 'Normally, when one client acquires a WRITE lock on a MyISAM table, other clients are not blocked from issuing statements that read from the table if the query results are present in the query cache. Setting this variable to 1 causes acquisition of a WRITE lock for a table to invalidate any queries in the query cache that refer to the table. This forces other clients that attempt to access the table to wait while the lock is in effect.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'query_prealloc_size', '8192', 1, '[8192-1048576]', 'Persistent buffer for query parsing and execution', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'range_alloc_block_size', '4096', 1, '[4096-18446744073709551615]', 'The size of blocks that are allocated when doing range optimization.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'range_optimizer_max_mem_size', '8388608', 0, '[0-18446744073709551615]', 'The limit on memory consumption for the range optimizer. A value of 0 means “no limit.” If an execution plan considered by the optimizer uses the range access method but the optimizer estimates that the amount of memory needed for this method would exceed the limit, it abandons the plan and considers other plans. For more information, see Limiting Memory Use for Range Optimization.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'session_track_gtids', 'OFF', 1, '[OFF|OWN_GTID|ALL_GTIDS]OFF', 'Controls a tracker for capturing GTIDs and returning them in the OK packet. Depending on the value of this option, at the end of executing a transaction, the GTIDs specified are captured by the tracker and appended to the OK packet.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'session_track_schema', 'ON', 1, '[ON|OFF]', 'Controls whether the server tracks changes to the default schema (database) name within the current session and makes this information available to the client when changes occur.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'session_track_state_change', 'OFF', 1, '[ON|OFF]', 'Controls whether the server tracks changes to the state of the current session and notifies the client when state changes occur. ', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'sha256_password_proxy_users', 'OFF', 1, '[ON|OFF]', 'This variable controls whether the sha256_password built-in authentication plugin supports proxy users. It has no effect unless the check_proxy_users system variable is enabled.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'show_compatibility_56', 'OFF', 1, '[ON|OFF]', 'The INFORMATION_SCHEMA has tables that contain system and status variable information  The Performance Schema tables are intended to replace the INFORMATION_SCHEMA tables, which are deprecated as of MySQL 5.7.6 and will be removed in a future MySQL release.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'show_old_temporals', 'OFF', 1, '[ON|OFF]', 'Whether SHOW CREATE TABLE output includes comments to flag temporal columns found to be in pre-5.6.4 format (TIME, DATETIME, and TIMESTAMP columns without support for fractional seconds precision). This variable is disabled by default. If enabled', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'slave_net_timeout', '60', 1, '[15-300]', 'The number of seconds to wait for more data from the master before the slave considers the connection broken, aborts the read, and tries to reconnect', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'slave_parallel_type', 'LOGICAL_CLOCK', 0, 'DATABASE,LOGICAL_CLOCK', 'Specifies if the slave will use database partitioning or information from master to parallelize transactions.(Default: DATABASE).', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'slow_launch_time', '2', 1, '[1-1024]', 'If creating the thread takes longer than this value (in seconds), the Slow_launch_threads counter will be incremented', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'slow_query_log', 'ON', 1, '[ON|OFF]', 'Specify the initial slow query log state. With no argument or an argument of 1, the --slow-query-log option enables the log. If omitted or given with an argument of 0, the option disables the log.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'sort_buffer_size', '262144', 1, '[32768-4294967295]', 'Each session that must perform a sort allocates a buffer of this size. ', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'sql_mode', '', 1, '(\\s*|REAL_AS_FLOAT|PIPES_AS_CONCAT|ANSI_QUOTES|IGNORE_SPACE|ONLY_FULL_GROUP_BY|NO_UNSIGNED_SUBTRACTION|NO_DIR_IN_CREATE|POSTGRESQL|ORACLE|MSSQL|DB2|MAXDB|NO_KEY_OPTIONS|NO_TABLE_OPTIONS|NO_FIELD_OPTIONS|MYSQL323|MYSQL40|ANSI|NO_AUTO_VALUE_ON_ZERO|NO_BACKSLASH_ESCAPES|STRICT_TRANS_TABLES|STRICT_ALL_TABLES|NO_ZERO_IN_DATE|NO_ZERO_DATE|ALLOW_INVALID_DATES|ERROR_FOR_DIVISION_BY_ZERO|TRADITIONAL|HIGH_NOT_PRECEDENCE|NO_ENGINE_SUBSTITUTION|PAD_CHAR_TO_FULL_LENGTH)(,REAL_AS_FLOAT|,PIPES_AS_CONCAT|,ANSI_QUOTES|,IGNORE_SPACE|,ONLY_FULL_GROUP_BY|,NO_UNSIGNED_SUBTRACTION|,NO_DIR_IN_CREATE|,POSTGRESQL|,ORACLE|,MSSQL|,DB2|,MAXDB|,NO_KEY_OPTIONS|,NO_TABLE_OPTIONS|,NO_FIELD_OPTIONS|,MYSQL323|,MYSQL40|,ANSI|,NO_AUTO_VALUE_ON_ZERO|,NO_BACKSLASH_ESCAPES|,STRICT_TRANS_TABLES|,STRICT_ALL_TABLES|,NO_ZERO_IN_DATE|,NO_ZERO_DATE|,ALLOW_INVALID_DATES|,ERROR_FOR_DIVISION_BY_ZERO|,TRADITIONAL|,HIGH_NOT_PRECEDENCE|,NO_ENGINE_SUBSTITUTION|,PAD_CHAR_TO_FULL_LENGTH)*', 'Syntax: sql-mode=mode[,mode[,mode...]]. See the manual for the complete list of valid sql modes', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'stored_program_cache', '256', 1, '[16-524288]', 'Sets a soft upper limit for the number of cached stored routines per connection. The value of this variable is specified in terms of the number of stored routines held in each of the two caches maintained by the MySQL Server for, respectively, stored procedures and stored functions.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'table_definition_cache', '512', 1, '[400-80480]', 'The number of cached table definitions', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'table_open_cache', '2000', 1, '[1-524288]', 'The number of cached open tables (total for all table cache instances)', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'table_open_cache_instances', '1', 0, '[1-64]', 'The number of open tables cache instances. To improve scalability by reducing contention among sessions, the open tables cache can be partitioned into several smaller cache instances of size table_open_cache / table_open_cache_instances . A session needs to lock only one instance to access it for DML statements. This segments cache access among instances, permitting higher performance for operations that use the cache when there are many sessions accessing tables. (DDL statements still require a lock on the entire cache, but such statements are much less frequent than DML statements.)', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'thread_cache_size', '100', 1, '[0-16384]', 'How many threads the server should cache for reuse. When a client disconnects, the client''s threads are put in the cache if there are fewer than thread_cache_size threads there. Requests for threads are satisfied by reusing threads taken from the cache if possible, and only when the cache is empty is a new thread created. This variable can be increased to improve performance if you have a lot of new connections. Normally, this does not provide a notable performance improvement if you have a good thread implementation. However, if your server sees hundreds of connections per second you should normally set thread_cache_size high enough so that most new connections use cached threads. By examining the difference between the Connections and Threads_created status variables, you can see how efficient the thread cache is.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'thread_stack', '262144', 0, '[131072-1073741824]', 'The stack size for each thread. The default of 192KB (256KB for 64-bit systems) is large enough for normal operation. If the thread stack size is too small, it limits the complexity of the SQL statements that the server can handle, the recursion depth of stored procedures, and other memory-consuming actions.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'tls_version', 'TLSv1,TLSv1.1,TLSv1.2', 0, '[TLSv1,TLSv1.1,TLSv1.2|TLSv1,TLSv1.1]', 'The protocols permitted by the server for encrypted connections. The value is a comma-separated list containing one or more protocol names. The protocols that can be named for this variable depend on the SSL library used to compile MySQL.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'tmp_table_size', '2097152', 1, '[262144-67108864]', 'If an internal in-memory temporary table exceeds this size, MySQL will automatically convert it to an on-disk MyISAM table', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'transaction_alloc_block_size', '8192', 1, '[1024-131072]', 'The amount in bytes by which to increase a per-transaction memory pool which needs memory. See the description of transaction_prealloc_size.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'transaction_isolation', 'READ-COMMITTED', 1, '[READ-UNCOMMITTED|READ-COMMITTED|REPEATABLE-READ|SERIALIZABLE]', 'Default transaction isolation level', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'transaction_prealloc_size', '4096', 1, '[1024-131072]', 'There is a per-transaction memory pool from which various transaction-related allocations take memory. The initial size of the pool in bytes is transaction_prealloc_size. For every allocation that cannot be satisfied from the pool because it has insufficient memory available, the pool is increased by transaction_alloc_block_size bytes. When the transaction ends, the pool is truncated to transaction_prealloc_size bytes.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'updatable_views_with_limit', 'YES', 1, '[YES|NO]', 'This variable controls whether updates to a view can be made when the view does not contain all columns of the primary key defined in the underlying table, if the update statement contains a LIMIT clause. (Such updates often are generated by GUI tools.) An update is an UPDATE or DELETE statement. Primary key here means a PRIMARY KEY, or a UNIQUE index in which no column can contain NULL.', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
INSERT INTO param_template (db_type, variable_name, default_value, editable, valid_values, description, create_time, sys_time) VALUES ('mysql', 'wait_timeout', '86400', 1, '[1-31536000]', 'The number of seconds the server waits for activity on a connection before closing it', '2019-04-08 01:54:42', '2019-04-08 01:54:42');
