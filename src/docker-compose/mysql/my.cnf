[mysqld_safe]
socket          = /var/run/mysqld/mysqld.sock
nice            = 0

[mysqld]
pid-file        = /var/run/mysqld/mysqld.pid
socket          = /var/run/mysqld/mysqld.sock
port            = 3306
basedir         = /usr
datadir         = /var/lib/mysql
tmpdir          = /tmp
lc-messages-dir = /usr/share/mysql
skip-external-locking
lower_case_table_names=1
default-time_zone = '+8:00'

innodb_buffer_pool_size = 512M

server-id              = 100
log_bin                        = /var/lib/mysql/mysql-bin.log
expire_logs_days        = 1
max_binlog_size         = 500M
max_allowed_packet      = 1024M

character-set-server = utf8mb4
collation-server = utf8mb4_general_ci

slow_query_log_file = /var/lib/mysql/mysql-slow.log
slow_query_log      = 1
long_query_time = 1

[client]
default-character-set=utf8mb4

[mysqldump]
quick
quote-names
max_allowed_packet      = 1024M


!includedir /etc/mysql/conf.d/
