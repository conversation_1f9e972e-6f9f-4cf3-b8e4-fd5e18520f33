#!/bin/bash

cd /opt/archery

echo 设置uv环境
source $HOME/.cargo/env

echo 修改重定向端口
if [[ -z $NGINX_PORT ]]; then
    sed -i "s/:nginx_port//g" /etc/nginx/nginx.conf
else
    sed -i "s/nginx_port/$NGINX_PORT/g" /etc/nginx/nginx.conf
fi

echo 启动nginx
/usr/sbin/nginx

echo 收集所有的静态文件到STATIC_ROOT
uv run python manage.py collectstatic -v0 --noinput

echo 启动Django Q cluster
uv run supervisord -c /etc/supervisord.conf

echo 启动服务
uv run gunicorn -w 4 -b 127.0.0.1:8888 --timeout 600 archery.wsgi:application








