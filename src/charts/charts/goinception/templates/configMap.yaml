{{- if .Values.configMap.enabled -}}
kind: ConfigMap
apiVersion: v1
metadata:
  name: goinception-config
  labels:
    app.kubernetes.io/name: {{ include "goinception.name" . }}
    helm.sh/chart: {{ include "goinception.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- with .Values.configMap.data }}
data:
{{- toYaml . | nindent 2 }}
{{- end }}
{{- end }}
