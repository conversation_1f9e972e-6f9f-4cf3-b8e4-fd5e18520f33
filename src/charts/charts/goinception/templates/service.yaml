apiVersion: v1
kind: Service
metadata:
  name: {{ include "goinception.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "goinception.name" . }}
    helm.sh/chart: {{ include "goinception.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: goinception
  selector:
    app.kubernetes.io/name: {{ include "goinception.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
