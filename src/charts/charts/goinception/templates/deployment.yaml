apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "goinception.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "goinception.name" . }}
    helm.sh/chart: {{ include "goinception.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "goinception.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "goinception.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      initContainers:
        - name: override-configs
          image: busybox:1.28
          {{- with .Values.initEnv }}
          env:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          command: ['sh', '-c',
                    'sed "s/BACKUP_PASSWORD_PLACEHOLDER/${BACKUP_PASSWORD}/g" /etc/goinception-template/config.toml.template > /etc/goinception/config.toml']
          volumeMounts:
            - name: goinception-config-volume
              mountPath: /etc/goinception
            - name: goinception-config-template
              mountPath: /etc/goinception-template
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: goinception
              containerPort: 4000
              protocol: TCP
          livenessProbe:
            tcpSocket:
              port: goinception
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          command:
            - "/usr/local/bin/dumb-init"
            - "/goInception"
            - "--config=/etc/goinception/config.toml"
          volumeMounts:
            - name: goinception-config-volume
              mountPath: /etc/goinception
      volumes:
        - name: goinception-config-volume
          emptyDir: {}
        - name: goinception-config-template
          configMap:
            name: goinception-config
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
