apiVersion: v1
kind: Service
metadata:
  name: {{ include "archery.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "archery.name" . }}
    helm.sh/chart: {{ include "archery.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: archery
  selector:
    app.kubernetes.io/name: {{ include "archery.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
