apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "archery.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "archery.name" . }}
    helm.sh/chart: {{ include "archery.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "archery.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "archery.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      initContainers:
        {{- if .Values.redis.embedded }}
        - image: busybox
          name: init-mysql
          imagePullPolicy: IfNotPresent
          command: ['sh', '-c', 'until nslookup archery-mysql; do echo waiting for mysql; sleep 10; done;']
        {{- end}}
        - image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          name: init-archery
          imagePullPolicy: IfNotPresent
          env:
            - name: NGINX_PORT
              value: "9123"
          {{- if .Values.mysql.embedded }}
            - name: DATABASE_URL
              value: mysql://root:{{ .Values.mysql.auth.rootPassword }}@{{ include "archery.fullname" . }}-mysql/{{ .Values.mysql.auth.database }}
          {{- else if .Values.mysql.url }}
            - name: DATABASE_URL
              value: {{ .Values.mysql.url }}
          {{- else if .Values.mysql.urlSecret}}
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.mysql.urlSecret }}
                  key: {{ .Values.mysql.urlSecretKey }}
          {{- end}}
          {{- if .Values.redis.embedded }}
            - name: CACHE_URL
              value: redis://root:{{ .Values.redis.auth.rootPassword }}@{{ include "archery.fullname" . }}-redis:6379/0
            - name: DINGDING_CACHE_URL
              value: redis://root:{{ .Values.redis.auth.rootPassword }}@{{ include "archery.fullname" . }}-redis:6379/1
          {{- else if .Values.redis.url }}
            - name: CACHE_URL
              value: {{ .Values.redis.url }}
            - name: DINGDING_CACHE_URL
              value: {{ .Values.redis.dingdingUrl }}
          {{- else if .Values.redis.urlSecret}}
            - name: CACHE_URL
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.redis.urlSecret }}
                  key: {{ .Values.redis.urlSecretKey }}
            - name: DINGDING_CACHE_URL
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.redis.dingdingUrlSecret }}
                  key: {{ .Values.redis.dingdingUrlSecretKey }}
          {{- end }}
          {{- with .Values.envs }}
            {{ toYaml . | nindent 12 }}
          {{- end}}
          volumeMounts:
          - name: archery-settings
            subPath: local_settings.py
            mountPath: /opt/archery/local_settings.py
          - name: archery-settings
            subPath: init-archery.sh
            mountPath: /opt/archery/src/docker/init-archery.sh
          - name: archery-settings
            subPath: createsuperuser.py
            mountPath: /opt/archery/src/docker/createsuperuser.py
          - mountPath: /opt/archery/downloads
            name: archery-download
          - mountPath: /opt/archery/src/script
            name: archery-script
          command: ['/bin/bash','/opt/archery/src/docker/init-archery.sh']
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: NGINX_PORT
              value: "9123"
          {{- if .Values.mysql.embedded }}
            - name: DATABASE_URL
              value: mysql://root:{{ .Values.mysql.auth.rootPassword }}@{{ include "archery.fullname" . }}-mysql/{{ .Values.mysql.auth.database }}
          {{- else if .Values.mysql.url }}
            - name: DATABASE_URL
              value: {{ .Values.mysql.url }}
          {{- else if .Values.mysql.urlSecret}}
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.mysql.urlSecret }}
                  key: {{ .Values.mysql.urlSecretKey }}
          {{- end}}
          {{- if .Values.redis.embedded }}
            - name: CACHE_URL
              value: redis://root:{{ .Values.redis.auth.rootPassword }}@{{ include "archery.fullname" . }}-redis:6379/0
            - name: DINGDING_CACHE_URL
              value: redis://root:{{ .Values.redis.auth.rootPassword }}@{{ include "archery.fullname" . }}-redis:6379/1
          {{- else if .Values.redis.url }}
            - name: CACHE_URL
              value: {{ .Values.redis.url }}
            - name: DINGDING_CACHE_URL
              value: {{ .Values.redis.dingdingUrl }}
          {{- else if .Values.redis.urlSecret}}
            - name: CACHE_URL
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.redis.urlSecret }}
                  key: {{ .Values.redis.urlSecretKey }}
            - name: DINGDING_CACHE_URL
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.redis.dingdingUrlSecret }}
                  key: {{ .Values.redis.dingdingUrlSecretKey }}
          {{- end }}
          {{- with .Values.envs }}
            {{ toYaml . | nindent 12 }}
          {{- end}}
          ports:
            - name: archery
              containerPort: 9123
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: archery
            initialDelaySeconds: 180
            periodSeconds: 60
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- with .Values.volumeMounts }}
          volumeMounts:
          {{- toYaml . | nindent 12 }}
         {{- end }}
     {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}  
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
