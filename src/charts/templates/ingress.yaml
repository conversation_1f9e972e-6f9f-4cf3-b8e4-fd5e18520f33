{{- if .Values.ingress.enabled -}}
{{- $fullName := include "archery.fullname" . -}}
{{- $ingressPaths := .Values.ingress.paths -}}
{{- $servicePort := .Values.ingress.servicePort -}}
{{- if .Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress" }}
apiVersion: networking.k8s.io/v1
{{- else if .Capabilities.APIVersions.Has "networking.k8s.io/v1beta1/Ingress" }}
apiVersion: networking.k8s.io/v1beta1
{{- else }}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    app.kubernetes.io/name: {{ include "archery.name" . }}
    helm.sh/chart: {{ include "archery.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.ingress.className (semverCompare ">=1.18-0" .Capabilities.KubeVersion.GitVersion) }}
  ingressClassName: {{ .Values.ingress.className }}
  {{- end }}
  rules:
    {{- range .Values.ingress.hosts }}
      {{- $url := splitList "/" . }}
    - host: {{ first $url }}
      http:
        paths:
          - path: /{{ rest $url | join "/" }}
            backend:
              {{- if $.Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress" }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $servicePort }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ $servicePort }}
              {{- end }}
            {{- if $.Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress" }}
            pathType: Prefix
            {{- end }}
    {{- end -}}
  {{- if .Values.ingress.tls }}
  tls:
{{ toYaml .Values.ingress.tls | indent 4 }}
  {{- end -}}
{{- end -}}
