[unix_http_server]
file=supervisor.sock

[supervisord]
logfile=logs/supervisord.log
nodaemon=false

[supervisorctl]
serverurl=unix://supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory=supervisor.rpcinterface:make_main_rpcinterface

[program:archery]
command=gunicorn -w 4 -b 127.0.0.1:8888 --timeout 600 archery.wsgi:application
autorestart=true
stopasgroup=true
killasgroup=true
redirect_stderr=true

[program:qcluster]
command=python manage.py qcluster
autorestart=true
stopasgroup=true
killasgroup=true
redirect_stderr=true

