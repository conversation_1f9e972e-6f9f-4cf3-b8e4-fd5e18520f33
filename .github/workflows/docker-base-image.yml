name: Publish Docker Base image

on:
  push:
    branches:
      - 'master'
    paths:
      - 'src/docker/Dockerfile-base'
  pull_request:
    branches:
      - 'master'
    paths:
      - 'src/docker/Dockerfile-base'

jobs:
  push_to_registries:
    name: Build and push Docker Base images
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - name: Check out the repo
        uses: actions/checkout@v2

      - name: Log in to Docker Hub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v3
        with:
          tags: |
            type=sha
          images: |
            hhyo/archery-base

      - name: Build and push Docker Base images
        uses: docker/build-push-action@v2
        with:
          context: .
          file: src/docker/Dockerfile-base
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
