name: Django CI

on:
  push:
    branches:
      - 'master'
    tags:
      - 'v*'
  pull_request:
    branches:
      - 'master'

jobs:
  django_ci:
    name: Build and Test a Django Project
    runs-on: ubuntu-latest
    strategy:
      max-parallel: 4
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11"]
  
    # https://github.com/actions/example-services/tree/master/.github/workflows
    services:
      mysql:
        image: mysql:5.7
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: yes
        ports:
        - 3306:3306
        options: >-
          --name=mysql 
          --health-cmd="mysqladmin ping" 
          --health-interval=10s 
          --health-timeout=5s 
          --health-retries=5
      redis:
        image: redis
        ports:
        - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Check out repository code
      uses: actions/checkout@v2
    
    - name: SET MySQL Cnf
      run: |
        cat << EOF > my.cnf
        [mysqld]
        server-id=100
        log_bin=ON
        character-set-server = utf8mb4
        collation-server = utf8mb4_general_ci
        lower_case_table_names=1
        default-time_zone = '+8:00'
        [client]
        default-character-set=utf8mb4
        EOF
        docker cp my.cnf mysql:/etc/mysql/conf.d/
        docker restart mysql
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"

    - name: Install Dependencies
      run: |
        sudo apt-get update && sudo apt-get install libsasl2-dev libkrb5-dev libldap2-dev libssl-dev unixodbc unixodbc-dev
        uv sync --all-extras
        
    - name: Init Table
      run: |
        mysql -h127.0.0.1 -uroot -e "CREATE DATABASE archery CHARSET UTF8MB4;"
        mysql -h127.0.0.1 -uroot -e "DROP DATABASE IF EXISTS test_archery;CREATE DATABASE test_archery CHARSET UTF8MB4;"

    - name: Run Tests
      run: |
        uv run python manage.py makemigrations
        uv run python manage.py makemigrations sql
        uv run pytest --cov --cov-report xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        files: ./coverage.xml
        fail_ci_if_error: true
