name: 🐛 报告BUG
description: 报告BUG请务必使用这个模板，不规范的提交可能会被直接关闭
title: "请对问题进行简要的描述"
body:
  - type: markdown
    attributes:
      value: |
        感谢你花费时间来报告这个问题，在提交前请先查阅 [WIKI](https://github.com/hhyo/archery/wiki) | [FAQ](https://github.com/hhyo/archery/wiki/FAQ) 等文档，确保这是一个需要修复的问题🐛。如果是对于使用方法的咨询🤔🙏，或者你有好的想法和建议💡，请通过[Discussions](https://github.com/hhyo/Archery/discussions)来发布，再次感谢你对Archery的贡献 🎉🎉🎉
  - type: textarea
    id: what-happened
    attributes:
      label: 重现步骤
      description: 现在告诉我们你遇到了什么问题，并描述问题出现场景和重现步骤
      placeholder: |
        1. 把冰箱门打开
        2. 把大象放进去
        3. 把冰箱门关上
    validations:
      required: true
  - type: textarea
    id: current-behavior
    attributes:
      label: 预期外的结果
      description: |
        告诉我们你遇到了什么错误，如果可以请提供相关截图信息
        
        Tip: 你可以将文件拖拽过来以添加图片，但请确保已经处理了其中包含的敏感信息
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: 日志文本
      description: |
        你可以将相关日志信息粘贴到此处，将会自动格式化处理，关于日志的查看方式
        1. logs/archery.log
        2. logs/qcluster.log
        3. docker logs archery -f --tail=10
      render: shell
    validations:
      required: false
  - type: input
    id: version
    attributes:
      label: 版本
      description: 请告诉我们你正在使用的Archery版本
      placeholder: 1.9.1
    validations:
      required: true
  - type: dropdown
    id: deploy
    attributes:
      label: 部署方式
      description: 请告诉我们你的应用部署方式
      options:
        - Docker
        - K8S
        - 手工部署
    validations:
      required: true
  - type: textarea
    id: other-msg
    attributes:
      label: 是否还有其他可以辅助定位问题的信息？比如数据库版本等
      description: |
        部分信息可以在Archery的debug api中获取：[参考debug地址](https://demo.archerydms.com/api/debug?full=true)
        - **MySQL**: 5.7.26
        - **Browsers**: Chrome
        - **goInception**: v1.2.5-49
    validations:
      required: false
