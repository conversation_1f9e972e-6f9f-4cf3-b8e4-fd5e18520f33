#!/usr/bin/env bash

#########################################################################
# Update Time : 2020-02-25
# <AUTHOR> <EMAIL>
#########################################################################

function init() {
    echo "Initing archery"
    echo "----------------"
    echo "安装/更新可能缺少的依赖: mysql-community-devel gcc gcc-c++ python-devel"
    sudo yum install -y epel-release
    sudo yum install -y mysql-devel gcc gcc-c++ python-devel MySQL-python
    sudo yum install -y python36 python3-devel python36-pip openldap-devel unixODBC-devel gettext

    # 检查是否安装了 uv，如果没有则安装
    if ! command -v uv &> /dev/null; then
        echo "Installing uv..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
        source $HOME/.cargo/env
    fi

    # 使用 uv 同步依赖
    uv sync
    echo "************************************************"
    echo -e "\033[32m init archery success \033[0m"
    echo -e "\033[32m welcome to archery 2.0 \033[0m"
}

function start() {
    echo "Starting archery"
    echo "----------------"
    uv run python manage.py collectstatic -v0 --noinput
    uv run supervisord -c supervisord.conf
    echo -e "Start archery:                 [\033[32m ok \033[0m]"
}

function stop() {
    echo "Stoping archery"
    echo "----------------"
    uv run supervisorctl -c supervisord.conf stop all
    kill -9 $(ps -ef | grep "Archery" | grep -v grep | awk '{print $2}')
    echo -e "Stop archery:                  [\033[32m ok \033[0m]"
}

function restart() {
    stop
    echo ""
    start
}

function adduser() {
    echo "Add Admin Users "
    uv run python manage.py createsuperuser
    echo -e "Add Users:                 [\033[32m ok \033[0m]"
}

function migration() {
    echo "Migration archery"
    echo "----------------"
    uv run python manage.py makemigrations sql
    uv run python manage.py migrate
    uv run python manage.py dbshell<sql/fixtures/auth_group.sql
    uv run python manage.py dbshell<src/init_sql/mysql_slow_query_review.sql
    if [ $? == "0" ]; then
        echo -e "Migration:                 [\033[32m ok \033[0m]"
    else
        echo -e "Migration:                 [\033[31m fail \033[0m]"
    fi
}

case "$1" in
    init )
        init
        ;;
    start )
        start
        ;;
    stop )
        stop
        ;;
    restart )
        restart
        ;;
    adduser )
        adduser
        ;;
    migration )
        migration
        ;;
    * )
        echo "************************************************"
        echo "Usage: sh admin {init|start|stop|restart|adduser|migration}"
        echo "************************************************"
        ;;
esac
