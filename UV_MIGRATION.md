# Archery 项目 UV 迁移指南

本项目已从传统的 pip + virtualenv 迁移到使用 [uv](https://github.com/astral-sh/uv) 进行依赖管理。uv 是一个极快的 Python 包管理器，提供了更好的性能和依赖解析能力。

## 什么是 uv？

uv 是由 Astral 开发的现代 Python 包管理器，具有以下优势：
- 🚀 极快的安装速度（比 pip 快 10-100 倍）
- 🔒 可靠的依赖解析
- 🎯 与现有 Python 生态系统兼容
- 📦 内置虚拟环境管理
- 🔧 支持 pyproject.toml 标准

## 安装 uv

### macOS 和 Linux
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Windows
```powershell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 使用 pip 安装
```bash
pip install uv
```

## 开发环境设置

### 1. 克隆项目
```bash
git clone https://github.com/hhyo/Archery.git
cd Archery
```

### 2. 同步依赖
```bash
# 安装所有依赖（包括开发依赖）
uv sync

# 仅安装生产依赖
uv sync --no-dev
```

### 3. 运行命令
```bash
# 运行 Django 管理命令
uv run python manage.py migrate
uv run python manage.py collectstatic
uv run python manage.py runserver

# 运行测试
uv run pytest

# 运行 linting
uv run flake8
```

## 常用命令对比

| 旧命令 (pip/virtualenv) | 新命令 (uv) |
|-------------------------|-------------|
| `pip install package` | `uv add package` |
| `pip install -r requirements.txt` | `uv sync` |
| `pip install -e .` | `uv sync` |
| `python -m venv venv && source venv/bin/activate` | `uv sync` (自动管理虚拟环境) |
| `source venv/bin/activate && python script.py` | `uv run python script.py` |

## 添加新依赖

### 添加生产依赖
```bash
uv add django-extensions
```

### 添加开发依赖
```bash
uv add --dev pytest-mock
```

### 从 PyPI 以外的源安装
```bash
uv add git+https://github.com/user/repo.git
```

## 项目结构变化

- ✅ `pyproject.toml` - 主要配置文件，包含所有依赖
- ✅ `.python-version` - 指定 Python 版本
- ✅ `uv.lock` - 锁定文件（自动生成，请提交到版本控制）
- ❌ `requirements.txt` - 已迁移到 pyproject.toml（保留用于向后兼容）
- ❌ `dev-requirements.txt` - 已迁移到 pyproject.toml

## 脚本更新

所有项目脚本已更新以使用 uv：
- `admin.sh` - 管理脚本
- `startup.sh` - 启动脚本
- `debug.sh` - 调试脚本

## Docker 支持

Docker 配置已更新以使用 uv：
- 基础镜像中安装 uv
- 使用 `uv sync` 安装依赖
- 使用 `uv run` 运行应用

## CI/CD 更新

GitHub Actions 工作流程已更新：
- 使用 `astral-sh/setup-uv@v3` action
- 使用 `uv sync --all-extras` 安装依赖
- 使用 `uv run` 运行测试

## 故障排除

### 常见问题

1. **命令找不到**
   ```bash
   # 确保 uv 在 PATH 中
   source $HOME/.cargo/env
   ```

2. **依赖冲突**
   ```bash
   # 清除缓存并重新同步
   uv cache clean
   uv sync --refresh
   ```

3. **Python 版本问题**
   ```bash
   # 检查 Python 版本
   uv python list
   # 安装特定版本
   uv python install 3.11
   ```

### 获取帮助

```bash
# 查看 uv 帮助
uv --help

# 查看特定命令帮助
uv sync --help
uv add --help
```

## 迁移检查清单

- [x] 创建 `pyproject.toml` 配置
- [x] 添加 `.python-version` 文件
- [x] 更新所有脚本文件
- [x] 更新 Docker 配置
- [x] 更新 CI/CD 配置
- [x] 更新文档

## 更多资源

- [uv 官方文档](https://docs.astral.sh/uv/)
- [uv GitHub 仓库](https://github.com/astral-sh/uv)
- [Python 包管理最佳实践](https://packaging.python.org/)
