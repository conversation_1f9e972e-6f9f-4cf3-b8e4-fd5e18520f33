[project]
name = "Archery"
version = "1.12.0"
description = "SQL审核查询平台"
readme = "README.md"
requires-python = ">=3.11"
authors = [
    {name = "hhyo", email = "<EMAIL>"}
]
license = {text = "Apache-2.0"}
keywords = ["sql", "audit", "query", "django"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Environment :: Web Environment",
    "Framework :: Django",
    "Framework :: Django :: 4.1",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Topic :: Database",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "Django==4.1.13",
    "mysqlclient>=2.0,<3.0",
    "requests==2.31.0",
    "simplejson==3.17.2",
    "mybatis_mapper2sql==0.1.9",
    "django-auth-ldap==4.1.0",
    "python-dateutil==2.8.1",
    "pymongo==4.6.3",
    "psycopg2-binary>=2.0,<3.0",
    "pymysql==0.9.3",
    "mysql-replication==0.22",
    "django-q==1.3.9",
    "django-redis==5.2.0",
    "redis==3.5.3",
    "pyodbc>=4.0,<5.0",
    "gunicorn==22.0.0",
    "pyecharts==2.0.4",
    "aliyun-python-sdk-rds==2.1.1",
    "cx-Oracle==7.3.0",
    "supervisor==4.1.0",
    "phoenixdb==1.2.1",
    "django-mirage-field==1.4.0",
    "schema-sync==0.9.7",
    "parsedatetime==2.4",
    "sshtunnel==0.1.5",
    "pycryptodome==3.19.1",
    "pyodps>=0.11,<1.0",
    "numpy>=1.0,<2.0",
    "pandas>=1.0,<3.0",
    "clickhouse-driver>=0.2,<1.0",
    "djangorestframework==3.13.1",
    "djangorestframework-simplejwt==5.2.0",
    "django-filter==21.1",
    "drf-spectacular==0.22.0",
    "pyotp==2.6.0",
    "pillow==10.3.0",
    "qrcode==7.3.1",
    "django-environ==0.8.1",
    "alibabacloud_dysmsapi20170525==2.0.9",
    "tencentcloud-sdk-python==3.0.656",
    "mozilla-django-oidc==3.0.0",
    "django-auth-dingding==0.0.3",
    "django-cas-ng==4.3.0",
    "cassandra-driver",
    "httpx",
    "OpenAI",
    "elasticsearch==8.14.0",
    "opensearch_py==2.6.0",
    "setuptools>=80.9.0",
    "local-settings>=0.0.1",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-django",
    "pytest-mock",
    "pytest-cov",
    "codecov",
    "flake8",
]

[project.urls]
Homepage = "https://archerydms.com/"
Repository = "https://github.com/hhyo/Archery"
Documentation = "https://archerydms.com/"
"Bug Tracker" = "https://github.com/hhyo/Archery/issues"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest",
    "pytest-django",
    "pytest-mock",
    "pytest-cov",
    "codecov",
    "flake8",
]

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "archery.settings"
python_files = "tests.py test_*.py *_tests.py"

[tool.coverage.run]
source = [
    "."
]
omit = [
    # omit anything in a .local directory anywhere
    "src*",
    # omit everything in /usr
    "downloads*",
    # omit this single file
    "sql/migrations/*",
    "venv*"
]

[tool.coverage.report]
omit = [
    # omit anything in a .local directory anywhere
    "src*",
    # omit everything in /usr
    "downloads*",
    # omit this single file
    "sql/migrations/*",
    "venv*"
]
