# coding: utf-8

import logging
import requests
from urllib.parse import urlencode
from django.conf import settings
from common.config import SysConfig
from django.core.cache import cache

logger = logging.getLogger("default")


class FeishuOAuth:
    """飞书 OAuth 工具类"""
    
    def __init__(self):
        self.sys_config = SysConfig()
        self.app_id = self.sys_config.get("feishu_oauth_app_id") or settings.AUTH_FEISHU_APP_ID
        self.app_secret = self.sys_config.get("feishu_oauth_app_secret") or settings.AUTH_FEISHU_APP_SECRET
        self.redirect_uri = self.sys_config.get("feishu_oauth_redirect_uri") or settings.AUTH_FEISHU_REDIRECT_URI
        
    def get_authorization_url(self, state=None):
        """获取飞书授权登录 URL"""
        params = {
            'app_id': self.app_id,
            'redirect_uri': self.redirect_uri,
            'response_type': 'code',
            'scope': 'contact:user.id:readonly contact:user.email:readonly contact:user.employee_id:readonly'
        }
        if state:
            params['state'] = state
            
        return f"https://open.feishu.cn/open-apis/authen/v1/authorize?{urlencode(params)}"
    
    def get_app_access_token(self):
        """获取应用访问令牌"""
        # 优先从缓存获取
        cache_key = "feishu_oauth_app_access_token"
        token = cache.get(cache_key)
        if token:
            return token
            
        url = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal/"
        data = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        
        try:
            response = requests.post(url, json=data, timeout=10)
            result = response.json()
            
            if result.get("code") == 0:
                token = result.get("app_access_token")
                expire = result.get("expire", 7200)
                # 缓存时间设置为过期时间减去60秒
                cache.set(cache_key, token, timeout=expire - 60)
                return token
            else:
                logger.error(f"获取飞书应用访问令牌失败: {result}")
                return None
        except Exception as e:
            logger.error(f"获取飞书应用访问令牌异常: {e}")
            return None
    
    def get_user_access_token(self, code):
        """通过授权码获取用户访问令牌"""
        url = "https://open.feishu.cn/open-apis/authen/v1/access_token"
        headers = {
            "Authorization": f"Bearer {self.get_app_access_token()}",
            "Content-Type": "application/json; charset=utf-8"
        }
        data = {
            "grant_type": "authorization_code",
            "code": code
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=10)
            result = response.json()
            
            if result.get("code") == 0:
                return result.get("data")
            else:
                logger.error(f"获取飞书用户访问令牌失败: {result}")
                return None
        except Exception as e:
            logger.error(f"获取飞书用户访问令牌异常: {e}")
            return None
    
    def get_user_info(self, user_access_token):
        """获取用户信息"""
        url = "https://open.feishu.cn/open-apis/authen/v1/user_info"
        headers = {
            "Authorization": f"Bearer {user_access_token}",
            "Content-Type": "application/json; charset=utf-8"
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            result = response.json()
            
            if result.get("code") == 0:
                return result.get("data")
            else:
                logger.error(f"获取飞书用户信息失败: {result}")
                return None
        except Exception as e:
            logger.error(f"获取飞书用户信息异常: {e}")
            return None
    
    def get_user_departments(self, user_id):
        """获取用户所在部门信息"""
        url = f"https://open.feishu.cn/open-apis/contact/v3/users/{user_id}"
        headers = {
            "Authorization": f"Bearer {self.get_app_access_token()}",
            "Content-Type": "application/json; charset=utf-8"
        }
        params = {
            "user_id_type": "open_id"
        }
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            result = response.json()
            
            if result.get("code") == 0:
                user_data = result.get("data", {}).get("user", {})
                return user_data.get("department_ids", [])
            else:
                logger.error(f"获取飞书用户部门信息失败: {result}")
                return []
        except Exception as e:
            logger.error(f"获取飞书用户部门信息异常: {e}")
            return []
    
    def get_department_info(self, department_id):
        """获取部门信息"""
        url = f"https://open.feishu.cn/open-apis/contact/v3/departments/{department_id}"
        headers = {
            "Authorization": f"Bearer {self.get_app_access_token()}",
            "Content-Type": "application/json; charset=utf-8"
        }
        params = {
            "department_id_type": "department_id"
        }
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            result = response.json()
            
            if result.get("code") == 0:
                return result.get("data", {}).get("department", {})
            else:
                logger.error(f"获取飞书部门信息失败: {result}")
                return None
        except Exception as e:
            logger.error(f"获取飞书部门信息异常: {e}")
            return None
    
    def get_department_path(self, department_id):
        """获取部门路径（一级部门-二级部门-三级部门）"""
        path_parts = []
        current_dept_id = department_id
        
        # 最多查询5级部门，避免无限循环
        for _ in range(5):
            if not current_dept_id or current_dept_id == "0":
                break
                
            dept_info = self.get_department_info(current_dept_id)
            if not dept_info:
                break
                
            dept_name = dept_info.get("name")
            if dept_name:
                path_parts.insert(0, dept_name)
            
            # 获取父部门ID
            current_dept_id = dept_info.get("parent_department_id")
            if current_dept_id == "0":  # 根部门
                break
        
        return "-".join(path_parts) if path_parts else None
