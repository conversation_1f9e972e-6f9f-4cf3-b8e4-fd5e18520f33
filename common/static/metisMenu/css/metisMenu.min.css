/*!
* metismenu - v2.7.8
* A jQuery menu plugin
* https://github.com/onokumus/metismenu#readme
*
* Made by <PERSON><PERSON> <<EMAIL>> (https://github.com/onokumus)
* Under MIT License
*/.metismenu .arrow{float:right;line-height:1.42857}[dir=rtl] .metismenu .arrow{float:left}.metismenu .glyphicon.arrow:before{content:"\e079"}.metismenu .active>a>.glyphicon.arrow:before{content:"\e114"}.metismenu .fa.arrow:before{content:"\f104"}.metismenu .active>a>.fa.arrow:before{content:"\f107"}.metismenu .ion.arrow:before{content:"\f3d2"}.metismenu .active>a>.ion.arrow:before{content:"\f3d0"}.metismenu .plus-times{float:right}[dir=rtl] .metismenu .plus-times{float:left}.metismenu .fa.plus-times:before{content:"\f067"}.metismenu .active>a>.fa.plus-times{-webkit-transform:rotate(45deg);transform:rotate(45deg)}.metismenu .plus-minus{float:right}[dir=rtl] .metismenu .plus-minus{float:left}.metismenu .fa.plus-minus:before{content:"\f067"}.metismenu .active>a>.fa.plus-minus:before{content:"\f068"}.metismenu .collapse{display:none}.metismenu .collapse.in{display:block}.metismenu .collapsing{height:0;overflow:hidden;position:relative;transition-duration:.35s;transition-property:height,visibility;transition-timing-function:ease}.metismenu .has-arrow{position:relative}.metismenu .has-arrow:after{-webkit-transform:rotate(-45deg) translateY(-50%);-webkit-transform-origin:top;border-color:initial;border-style:solid;border-width:1px 0 0 1px;content:"";height:.5em;position:absolute;right:1em;top:50%;transform:rotate(-45deg) translateY(-50%);transform-origin:top;transition:all .3s ease-out;width:.5em}[dir=rtl] .metismenu .has-arrow:after{-webkit-transform:rotate(135deg) translateY(-50%);left:1em;right:auto;transform:rotate(135deg) translateY(-50%)}.metismenu .active>.has-arrow:after,.metismenu .has-arrow[aria-expanded=true]:after{-webkit-transform:rotate(-135deg) translateY(-50%);transform:rotate(-135deg) translateY(-50%)}[dir=rtl] .metismenu .active>.has-arrow:after,[dir=rtl] .metismenu .has-arrow[aria-expanded=true]:after{-webkit-transform:rotate(225deg) translateY(-50%);transform:rotate(225deg) translateY(-50%)}
/*# sourceMappingURL=metisMenu.min.css.map */
