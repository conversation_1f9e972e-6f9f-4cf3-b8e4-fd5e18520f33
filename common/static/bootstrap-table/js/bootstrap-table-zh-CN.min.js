/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.22.4
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";var n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},r=function(t){return t&&t.Math===Math&&t},e=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n&&n)||r("object"==typeof n&&n)||function(){return this}()||Function("return this")(),o={},i=function(t){try{return!!t()}catch(t){return!0}},u=!i((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),c=!i((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),a=c,f=Function.prototype.call,l=a?f.bind(f):function(){return f.apply(f,arguments)},s={},p={}.propertyIsEnumerable,b=Object.getOwnPropertyDescriptor,y=b&&!p.call({1:2},1);s.f=y?function(t){var n=b(this,t);return!!n&&n.enumerable}:p;var g,m,h=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},v=c,d=Function.prototype,w=d.call,S=v&&d.bind.bind(w,w),O=v?S:function(t){return function(){return w.apply(t,arguments)}},j=O,P=j({}.toString),T=j("".slice),E=function(t){return T(P(t),8,-1)},C=i,x=E,A=Object,F=O("".split),R=C((function(){return!A("z").propertyIsEnumerable(0)}))?function(t){return"String"===x(t)?F(t,""):A(t)}:A,M=function(t){return null==t},z=M,N=TypeError,k=function(t){if(z(t))throw new N("Can't call method on "+t);return t},I=R,L=k,D=function(t){return I(L(t))},_="object"==typeof document&&document.all,q=void 0===_&&void 0!==_?function(t){return"function"==typeof t||t===_}:function(t){return"function"==typeof t},G=q,B=function(t){return"object"==typeof t?null!==t:G(t)},U=e,W=q,$=function(t){return W(t)?t:void 0},H=function(t,n){return arguments.length<2?$(U[t]):U[t]&&U[t][n]},J=O({}.isPrototypeOf),K=e,Q="undefined"!=typeof navigator&&String(navigator.userAgent)||"",V=K.process,X=K.Deno,Y=V&&V.versions||X&&X.version,Z=Y&&Y.v8;Z&&(m=(g=Z.split("."))[0]>0&&g[0]<4?1:+(g[0]+g[1])),!m&&Q&&(!(g=Q.match(/Edge\/(\d+)/))||g[1]>=74)&&(g=Q.match(/Chrome\/(\d+)/))&&(m=+g[1]);var tt=m,nt=tt,rt=i,et=e.String,ot=!!Object.getOwnPropertySymbols&&!rt((function(){var t=Symbol("symbol detection");return!et(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&nt&&nt<41})),it=ot&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,ut=H,ct=q,at=J,ft=Object,lt=it?function(t){return"symbol"==typeof t}:function(t){var n=ut("Symbol");return ct(n)&&at(n.prototype,ft(t))},st=String,pt=q,bt=function(t){try{return st(t)}catch(t){return"Object"}},yt=TypeError,gt=function(t){if(pt(t))return t;throw new yt(bt(t)+" is not a function")},mt=M,ht=l,vt=q,dt=B,wt=TypeError,St={exports:{}},Ot=e,jt=Object.defineProperty,Pt=function(t,n){try{jt(Ot,t,{value:n,configurable:!0,writable:!0})}catch(r){Ot[t]=n}return n},Tt=e,Et=Pt,Ct="__core-js_shared__",xt=St.exports=Tt[Ct]||Et(Ct,{});(xt.versions||(xt.versions=[])).push({version:"3.36.0",mode:"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.36.0/LICENSE",source:"https://github.com/zloirock/core-js"});var At=St.exports,Ft=At,Rt=function(t,n){return Ft[t]||(Ft[t]=n||{})},Mt=k,zt=Object,Nt=function(t){return zt(Mt(t))},kt=Nt,It=O({}.hasOwnProperty),Lt=Object.hasOwn||function(t,n){return It(kt(t),n)},Dt=O,_t=0,qt=Math.random(),Gt=Dt(1..toString),Bt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Gt(++_t+qt,36)},Ut=Rt,Wt=Lt,$t=Bt,Ht=ot,Jt=it,Kt=e.Symbol,Qt=Ut("wks"),Vt=Jt?Kt.for||Kt:Kt&&Kt.withoutSetter||$t,Xt=function(t){return Wt(Qt,t)||(Qt[t]=Ht&&Wt(Kt,t)?Kt[t]:Vt("Symbol."+t)),Qt[t]},Yt=l,Zt=B,tn=lt,nn=function(t,n){var r=t[n];return mt(r)?void 0:gt(r)},rn=function(t,n){var r,e;if("string"===n&&vt(r=t.toString)&&!dt(e=ht(r,t)))return e;if(vt(r=t.valueOf)&&!dt(e=ht(r,t)))return e;if("string"!==n&&vt(r=t.toString)&&!dt(e=ht(r,t)))return e;throw new wt("Can't convert object to primitive value")},en=TypeError,on=Xt("toPrimitive"),un=function(t,n){if(!Zt(t)||tn(t))return t;var r,e=nn(t,on);if(e){if(void 0===n&&(n="default"),r=Yt(e,t,n),!Zt(r)||tn(r))return r;throw new en("Can't convert object to primitive value")}return void 0===n&&(n="number"),rn(t,n)},cn=lt,an=function(t){var n=un(t,"string");return cn(n)?n:n+""},fn=B,ln=e.document,sn=fn(ln)&&fn(ln.createElement),pn=function(t){return sn?ln.createElement(t):{}},bn=!u&&!i((function(){return 7!==Object.defineProperty(pn("div"),"a",{get:function(){return 7}}).a})),yn=u,gn=l,mn=s,hn=h,vn=D,dn=an,wn=Lt,Sn=bn,On=Object.getOwnPropertyDescriptor;o.f=yn?On:function(t,n){if(t=vn(t),n=dn(n),Sn)try{return On(t,n)}catch(t){}if(wn(t,n))return hn(!gn(mn.f,t,n),t[n])};var jn={},Pn=u&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Tn=B,En=String,Cn=TypeError,xn=function(t){if(Tn(t))return t;throw new Cn(En(t)+" is not an object")},An=u,Fn=bn,Rn=Pn,Mn=xn,zn=an,Nn=TypeError,kn=Object.defineProperty,In=Object.getOwnPropertyDescriptor,Ln="enumerable",Dn="configurable",_n="writable";jn.f=An?Rn?function(t,n,r){if(Mn(t),n=zn(n),Mn(r),"function"==typeof t&&"prototype"===n&&"value"in r&&_n in r&&!r.writable){var e=In(t,n);e&&e.writable&&(t[n]=r.value,r={configurable:Dn in r?r.configurable:e.configurable,enumerable:Ln in r?r.enumerable:e.enumerable,writable:!1})}return kn(t,n,r)}:kn:function(t,n,r){if(Mn(t),n=zn(n),Mn(r),Fn)try{return kn(t,n,r)}catch(t){}if("get"in r||"set"in r)throw new Nn("Accessors not supported");return"value"in r&&(t[n]=r.value),t};var qn=jn,Gn=h,Bn=u?function(t,n,r){return qn.f(t,n,Gn(1,r))}:function(t,n,r){return t[n]=r,t},Un={exports:{}},Wn=u,$n=Lt,Hn=Function.prototype,Jn=Wn&&Object.getOwnPropertyDescriptor,Kn=$n(Hn,"name"),Qn={EXISTS:Kn,PROPER:Kn&&"something"===function(){}.name,CONFIGURABLE:Kn&&(!Wn||Wn&&Jn(Hn,"name").configurable)},Vn=q,Xn=At,Yn=O(Function.toString);Vn(Xn.inspectSource)||(Xn.inspectSource=function(t){return Yn(t)});var Zn,tr,nr,rr=Xn.inspectSource,er=q,or=e.WeakMap,ir=er(or)&&/native code/.test(String(or)),ur=Bt,cr=Rt("keys"),ar={},fr=ir,lr=e,sr=B,pr=Bn,br=Lt,yr=At,gr=function(t){return cr[t]||(cr[t]=ur(t))},mr=ar,hr="Object already initialized",vr=lr.TypeError,dr=lr.WeakMap;if(fr||yr.state){var wr=yr.state||(yr.state=new dr);wr.get=wr.get,wr.has=wr.has,wr.set=wr.set,Zn=function(t,n){if(wr.has(t))throw new vr(hr);return n.facade=t,wr.set(t,n),n},tr=function(t){return wr.get(t)||{}},nr=function(t){return wr.has(t)}}else{var Sr=gr("state");mr[Sr]=!0,Zn=function(t,n){if(br(t,Sr))throw new vr(hr);return n.facade=t,pr(t,Sr,n),n},tr=function(t){return br(t,Sr)?t[Sr]:{}},nr=function(t){return br(t,Sr)}}var Or={set:Zn,get:tr,has:nr,enforce:function(t){return nr(t)?tr(t):Zn(t,{})},getterFor:function(t){return function(n){var r;if(!sr(n)||(r=tr(n)).type!==t)throw new vr("Incompatible receiver, "+t+" required");return r}}},jr=O,Pr=i,Tr=q,Er=Lt,Cr=u,xr=Qn.CONFIGURABLE,Ar=rr,Fr=Or.enforce,Rr=Or.get,Mr=String,zr=Object.defineProperty,Nr=jr("".slice),kr=jr("".replace),Ir=jr([].join),Lr=Cr&&!Pr((function(){return 8!==zr((function(){}),"length",{value:8}).length})),Dr=String(String).split("String"),_r=Un.exports=function(t,n,r){"Symbol("===Nr(Mr(n),0,7)&&(n="["+kr(Mr(n),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(n="get "+n),r&&r.setter&&(n="set "+n),(!Er(t,"name")||xr&&t.name!==n)&&(Cr?zr(t,"name",{value:n,configurable:!0}):t.name=n),Lr&&r&&Er(r,"arity")&&t.length!==r.arity&&zr(t,"length",{value:r.arity});try{r&&Er(r,"constructor")&&r.constructor?Cr&&zr(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=Fr(t);return Er(e,"source")||(e.source=Ir(Dr,"string"==typeof n?n:"")),t};Function.prototype.toString=_r((function(){return Tr(this)&&Rr(this).source||Ar(this)}),"toString");var qr=Un.exports,Gr=q,Br=jn,Ur=qr,Wr=Pt,$r={},Hr=Math.ceil,Jr=Math.floor,Kr=Math.trunc||function(t){var n=+t;return(n>0?Jr:Hr)(n)},Qr=function(t){var n=+t;return n!=n||0===n?0:Kr(n)},Vr=Qr,Xr=Math.max,Yr=Math.min,Zr=Qr,te=Math.min,ne=function(t){var n=Zr(t);return n>0?te(n,9007199254740991):0},re=function(t){return ne(t.length)},ee=D,oe=function(t,n){var r=Vr(t);return r<0?Xr(r+n,0):Yr(r,n)},ie=re,ue=function(t){return function(n,r,e){var o=ee(n),i=ie(o);if(0===i)return!t&&-1;var u,c=oe(e,i);if(t&&r!=r){for(;i>c;)if((u=o[c++])!=u)return!0}else for(;i>c;c++)if((t||c in o)&&o[c]===r)return t||c||0;return!t&&-1}},ce={includes:ue(!0),indexOf:ue(!1)},ae=Lt,fe=D,le=ce.indexOf,se=ar,pe=O([].push),be=function(t,n){var r,e=fe(t),o=0,i=[];for(r in e)!ae(se,r)&&ae(e,r)&&pe(i,r);for(;n.length>o;)ae(e,r=n[o++])&&(~le(i,r)||pe(i,r));return i},ye=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],ge=be,me=ye.concat("length","prototype");$r.f=Object.getOwnPropertyNames||function(t){return ge(t,me)};var he={};he.f=Object.getOwnPropertySymbols;var ve=H,de=$r,we=he,Se=xn,Oe=O([].concat),je=ve("Reflect","ownKeys")||function(t){var n=de.f(Se(t)),r=we.f;return r?Oe(n,r(t)):n},Pe=Lt,Te=je,Ee=o,Ce=jn,xe=i,Ae=q,Fe=/#|\.prototype\./,Re=function(t,n){var r=ze[Me(t)];return r===ke||r!==Ne&&(Ae(n)?xe(n):!!n)},Me=Re.normalize=function(t){return String(t).replace(Fe,".").toLowerCase()},ze=Re.data={},Ne=Re.NATIVE="N",ke=Re.POLYFILL="P",Ie=Re,Le=e,De=o.f,_e=Bn,qe=function(t,n,r,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:n;if(Gr(r)&&Ur(r,i,e),e.global)o?t[n]=r:Wr(n,r);else{try{e.unsafe?t[n]&&(o=!0):delete t[n]}catch(t){}o?t[n]=r:Br.f(t,n,{value:r,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},Ge=Pt,Be=function(t,n,r){for(var e=Te(n),o=Ce.f,i=Ee.f,u=0;u<e.length;u++){var c=e[u];Pe(t,c)||r&&Pe(r,c)||o(t,c,i(n,c))}},Ue=Ie,We=function(t,n){var r,e,o,i,u,c=t.target,a=t.global,f=t.stat;if(r=a?Le:f?Le[c]||Ge(c,{}):Le[c]&&Le[c].prototype)for(e in n){if(i=n[e],o=t.dontCallGetSet?(u=De(r,e))&&u.value:r[e],!Ue(a?e:c+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Be(i,o)}(t.sham||o&&o.sham)&&_e(i,"sham",!0),qe(r,e,i,t)}},$e=E,He=Array.isArray||function(t){return"Array"===$e(t)},Je=TypeError,Ke=u,Qe=jn,Ve=h,Xe={};Xe[Xt("toStringTag")]="z";var Ye="[object z]"===String(Xe),Ze=q,to=E,no=Xt("toStringTag"),ro=Object,eo="Arguments"===to(function(){return arguments}()),oo=O,io=i,uo=q,co=Ye?to:function(t){var n,r,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,n){try{return t[n]}catch(t){}}(n=ro(t),no))?r:eo?to(n):"Object"===(e=to(n))&&Ze(n.callee)?"Arguments":e},ao=rr,fo=function(){},lo=H("Reflect","construct"),so=/^\s*(?:class|function)\b/,po=oo(so.exec),bo=!so.test(fo),yo=function(t){if(!uo(t))return!1;try{return lo(fo,[],t),!0}catch(t){return!1}},go=function(t){if(!uo(t))return!1;switch(co(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return bo||!!po(so,ao(t))}catch(t){return!0}};go.sham=!0;var mo=!lo||io((function(){var t;return yo(yo.call)||!yo(Object)||!yo((function(){t=!0}))||t}))?go:yo,ho=He,vo=mo,wo=B,So=Xt("species"),Oo=Array,jo=function(t){var n;return ho(t)&&(n=t.constructor,(vo(n)&&(n===Oo||ho(n.prototype))||wo(n)&&null===(n=n[So]))&&(n=void 0)),void 0===n?Oo:n},Po=i,To=tt,Eo=Xt("species"),Co=We,xo=i,Ao=He,Fo=B,Ro=Nt,Mo=re,zo=function(t){if(t>9007199254740991)throw Je("Maximum allowed index exceeded");return t},No=function(t,n,r){Ke?Qe.f(t,n,Ve(0,r)):t[n]=r},ko=function(t,n){return new(jo(t))(0===n?0:n)},Io=function(t){return To>=51||!Po((function(){var n=[];return(n.constructor={})[Eo]=function(){return{foo:1}},1!==n[t](Boolean).foo}))},Lo=tt,Do=Xt("isConcatSpreadable"),_o=Lo>=51||!xo((function(){var t=[];return t[Do]=!1,t.concat()[0]!==t})),qo=function(t){if(!Fo(t))return!1;var n=t[Do];return void 0!==n?!!n:Ao(t)};Co({target:"Array",proto:!0,arity:1,forced:!_o||!Io("concat")},{concat:function(t){var n,r,e,o,i,u=Ro(this),c=ko(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(qo(i=-1===n?u:arguments[n]))for(o=Mo(i),zo(a+o),r=0;r<o;r++,a++)r in i&&No(c,a,i[r]);else zo(a+1),No(c,a++,i);return c.length=a,c}});var Go=be,Bo=ye,Uo=Object.keys||function(t){return Go(t,Bo)},Wo=u,$o=O,Ho=l,Jo=i,Ko=Uo,Qo=he,Vo=s,Xo=Nt,Yo=R,Zo=Object.assign,ti=Object.defineProperty,ni=$o([].concat),ri=!Zo||Jo((function(){if(Wo&&1!==Zo({b:1},Zo(ti({},"a",{enumerable:!0,get:function(){ti(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},n={},r=Symbol("assign detection"),e="abcdefghijklmnopqrst";return t[r]=7,e.split("").forEach((function(t){n[t]=t})),7!==Zo({},t)[r]||Ko(Zo({},n)).join("")!==e}))?function(t,n){for(var r=Xo(t),e=arguments.length,o=1,i=Qo.f,u=Vo.f;e>o;)for(var c,a=Yo(arguments[o++]),f=i?ni(Ko(a),i(a)):Ko(a),l=f.length,s=0;l>s;)c=f[s++],Wo&&!Ho(u,a,c)||(r[c]=a[c]);return r}:Zo,ei=ri;We({target:"Object",stat:!0,arity:2,forced:Object.assign!==ei},{assign:ei}),t.fn.bootstrapTable.locales["zh-CN"]=t.fn.bootstrapTable.locales.zh={formatCopyRows:function(){return"复制行"},formatPrint:function(){return"打印"},formatLoadingMessage:function(){return"正在努力地加载数据中，请稍候"},formatRecordsPerPage:function(t){return"每页显示 ".concat(t," 条记录")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"显示第 ".concat(t," 到第 ").concat(n," 条记录，总共 ").concat(r," 条记录（从 ").concat(e," 总记录中过滤）"):"显示第 ".concat(t," 到第 ").concat(n," 条记录，总共 ").concat(r," 条记录")},formatSRPaginationPreText:function(){return"上一页"},formatSRPaginationPageText:function(t){return"第".concat(t,"页")},formatSRPaginationNextText:function(){return"下一页"},formatDetailPagination:function(t){return"总共 ".concat(t," 条记录")},formatClearSearch:function(){return"清空过滤"},formatSearch:function(){return"搜索"},formatNoMatches:function(){return"没有找到匹配的记录"},formatPaginationSwitch:function(){return"隐藏/显示分页"},formatPaginationSwitchDown:function(){return"显示分页"},formatPaginationSwitchUp:function(){return"隐藏分页"},formatRefresh:function(){return"刷新"},formatToggleOn:function(){return"显示卡片视图"},formatToggleOff:function(){return"隐藏卡片视图"},formatColumns:function(){return"列"},formatColumnsToggleAll:function(){return"切换所有"},formatFullscreen:function(){return"全屏"},formatAllRows:function(){return"所有"},formatAutoRefresh:function(){return"自动刷新"},formatExport:function(){return"导出数据"},formatJumpTo:function(){return"跳转"},formatAdvancedSearch:function(){return"高级搜索"},formatAdvancedCloseButton:function(){return"关闭"},formatFilterControlSwitch:function(){return"隐藏/显示过滤控制"},formatFilterControlSwitchHide:function(){return"隐藏过滤控制"},formatFilterControlSwitchShow:function(){return"显示过滤控制"}},Object.assign(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["zh-CN"])}));
