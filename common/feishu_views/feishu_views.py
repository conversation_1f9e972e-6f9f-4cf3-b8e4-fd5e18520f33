# coding: utf-8

import logging
from django.contrib.auth import login
from django.http import HttpResponseRedirect, HttpResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from common.utils.feishu_oauth import FeishuOAuth
from common.authenticate.feishu_auth import FeishuAuthenticationBackend

logger = logging.getLogger("default")


def feishu_login(request):
    """飞书登录入口"""
    feishu_oauth = FeishuOAuth()
    
    # 生成状态参数，用于防止CSRF攻击
    state = request.session.session_key or "archery_feishu_login"
    request.session['feishu_oauth_state'] = state
    
    # 获取授权URL并重定向
    auth_url = feishu_oauth.get_authorization_url(state=state)
    return HttpResponseRedirect(auth_url)


@csrf_exempt
def feishu_callback(request):
    """飞书OAuth回调处理"""
    code = request.GET.get('code')
    state = request.GET.get('state')
    error = request.GET.get('error')
    
    # 检查是否有错误
    if error:
        logger.error(f"飞书OAuth授权失败: {error}")
        return render(request, 'login.html', {
            'error_message': f'飞书授权失败: {error}'
        })
    
    # 检查授权码
    if not code:
        logger.error("飞书OAuth回调缺少授权码")
        return render(request, 'login.html', {
            'error_message': '飞书授权失败: 缺少授权码'
        })
    
    # 验证状态参数
    expected_state = request.session.get('feishu_oauth_state')
    if state != expected_state:
        logger.error(f"飞书OAuth状态参数不匹配: expected={expected_state}, actual={state}")
        return render(request, 'login.html', {
            'error_message': '飞书授权失败: 状态参数不匹配'
        })
    
    # 使用认证后端进行认证
    backend = FeishuAuthenticationBackend()
    user = backend.authenticate(request, code=code)
    
    if user:
        # 登录用户
        login(request, user, backend='common.authenticate.feishu_auth.FeishuAuthenticationBackend')
        logger.info(f"飞书用户登录成功: {user.username}")
        
        # 清理session中的状态参数
        if 'feishu_oauth_state' in request.session:
            del request.session['feishu_oauth_state']
        
        # 重定向到首页
        return HttpResponseRedirect('/')
    else:
        logger.error("飞书用户认证失败")
        return render(request, 'login.html', {
            'error_message': '飞书登录失败，请联系管理员'
        })
