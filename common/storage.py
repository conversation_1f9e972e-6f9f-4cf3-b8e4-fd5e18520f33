# -*- coding: UTF-8 -*-
"""
@author: hhyo
@license: Apache Licence
@file: storage.py
@time: 2019/06/01
"""

__author__ = "hhyo"

from django.contrib.staticfiles.storage import ManifestStaticFilesStorage


class ForgivingManifestStaticFilesStorage(ManifestStaticFilesStorage):
    manifest_strict = False

    def hashed_name(self, name, content=None, filename=None):
        try:
            result = super().hashed_name(name, content, filename)
        except ValueError:
            # When the file is missing, let's forgive and ignore that.
            result = name
        return result
