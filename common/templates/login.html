<!DOCTYPE html>
<html>
<head>
    <title>Login To Archery{{ custom_title_suffix }}</title>
    {% load static %}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引入 Bootstrap -->
    <link href="{% static 'bootstrap/css/bootstrap.min.css' %}" rel="stylesheet">
    <link href="{% static 'dist/css/login.css' %}" rel="stylesheet">
    <!-- HTML5 Shim 和 Respond.js 用于让 IE8 支持 HTML5元素和媒体查询 -->
    <!-- 注意： 如果通过 file://  引入 Respond.js 文件，则该文件无法起效果 -->
    <!--[if lt IE 9]>
         <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
         <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
      <![endif]-->
    <link rel="shortcut icon" href="{% static 'img/favicon.ico' %}" />
</head>
<body style="background-color:#edeff1;">
<div class="row lsb-login">
    <div class="col-sm-4 login-form-wrapper">
        <h3 class="text-center">Login To Archery{{ custom_title_suffix }}</h3>
        <form class="login-form fade-in-effect" id="login" method="post" role="form">
            {% csrf_token %}
            {% if dingding_enabled %}
                <div class="form-group">
                    <a class="btn btn-primary btn-block" role="button" href="/dingding/authenticate/">以钉钉登录</a>
                </div>
            {% elif feishu_enabled %}
                <div class="form-group">
                    <a class="btn btn-primary btn-block" role="button" href="/feishu/login/">{{ feishu_btn_name }}</a>
                </div>
            {% elif oidc_enabled %}
                <div class="form-group">
                    <a class="btn btn-primary btn-block" role="button" href="/oidc/authenticate/">{{ oidc_btn_name }}</a>
                </div>
            {% elif cas_enabled %}
            <div class="form-group">
                <a class="btn btn-primary btn-block" role="button" href="/cas/authenticate/">CAS认证登录</a>
            </div>
            {% endif %}
            {% if dingding_enabled or feishu_enabled or oidc_enabled %}
                <a data-toggle="collapse" href="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                  显示传统登录
                </a>
                <div class="collapse" id="collapseExample">
                  <div>
                    {% include 'legacy_login_form.html' %}
                  </div>
                </div>
            {% else %}
                {% include 'legacy_login_form.html' %}
            {% endif %}
            {% if sign_up_enabled %}
                <div class="form-group">
                    <a href="#" data-toggle="modal" data-target="#sign-up">注册用户</a>
                </div>
            {% endif %}
        </form>
    </div>
</div>

<!-- 注册模态框-->
<div class="modal fade" id="sign-up" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">注册账号</h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <input type="text" autocomplete="off"
                           class="form-control" id="username" placeholder="用户名">
                </div>
                <div class="form-group">
                    <input type="text" autocomplete="off"
                           class="form-control" id="display" placeholder="中文名">
                </div>
                <div class="form-group">
                    <input type="text" autocomplete="off"
                           class="form-control" id="email" placeholder="邮箱">
                </div>
                <div class="form-group">
                    <input type="password"
                           class="form-control" id="password" placeholder="密码">
                </div>
                <div class="form-group">
                    <input type="password"
                           class="form-control" autocomplete="new-password" id="password2"
                           placeholder="密码确认">
                </div>

                <div class="form-group">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button id="btnSign" type="submit" class="btn btn-success">注册</button>
            </div>
        </div>
    </div>
</div>


<!-- 模态框（Modal） -->
<div class="modal fade" id="wrongpwd-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                    &times;
                </button>
                <h4 class="modal-title" id="myModalLabel">
                    提示
                </h4>
            </div>
            <div class="modal-body" id="wrongpwd-modal-body"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal">确定
                </button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal -->
</div>

<!--底部部分 -->
<div class="user-bottom-div">
    <p><strong>&copy; Archery</strong>&nbsp;(v{{ archery_version }})</p>
</div>
<!-- jQuery (Bootstrap 的 JavaScript 插件需要引入 jQuery，务必先引入jquery js再引入bootstrap js) -->
<script src="{% static 'jquery/jquery.min.js' %}"></script>
<!-- 包括所有已编译的插件 -->
<script src="{% static 'bootstrap/js/bootstrap.min.js' %}"></script>
</body>
<!-- 解决CSRF-->
<script>
    $(function () {
        $.ajaxSetup({
            headers: {"X-CSRFToken": getCookie("csrftoken")}
        });
    });

    function getCookie(name) {
        var cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            var cookies = document.cookie.split(';');
            for (var i = 0; i < cookies.length; i++) {
                var cookie = jQuery.trim(cookies[i]);
                // Does this cookie string begin with the name we want?
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
<script>
    <!-- 退出登录后清空sessionStorage -->
    $(document).ready(function () {
        sessionStorage.clear();
    });

    //回车键提交表单登录
    $(document).ready(function () {
        $(document).keydown(function (event) {
            //keycode==13为回车键
            console.log($("#sign-up").css("display"))
            if (event.keyCode === 13 && $("#sign-up").css("display") === 'none') {
                $('#btnLogin').addClass('disabled');
                $('#btnLogin').prop('disabled', true);
                let username = $('#inputUsername').val();
                let password = $('#inputPassword').val();
                authenticateUser(username, password);
            }
        });
    });

    $('#btnLogin').click(function () {
        $('#btnLogin').addClass('disabled');
        $('#btnLogin').prop('disabled', true);
        username = $('#inputUsername').val();
        password = $('#inputPassword').val();
        authenticateUser(username, password);
    });

    $('#btnSign').click(function () {
        $('#btnSign').addClass('disabled');
        $('#btnSign').prop('disabled', true);
        $.ajax({
            type: "post",
            url: "/signup/",
            dataType: "json",
            data: {
                username: $("#username").val(),
                password: $("#password").val(),
                password2: $("#password2").val(),
                display: $("#display").val(),
                email: $("#email").val(),
            },
            complete: function () {
                $('#btnSign').removeClass('disabled');
                $('#btnSign').prop('disabled', false);
            },
            success: function (data) {
                if (data.status === 0) {
                    $("#sign-up").modal('hide');
                    alert('注册成功, 请输入密码登录!');
                } else {
                    alert(data.msg);
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alert(errorThrown);
            }
        });
    });

    function authenticateUser(username, password) {
        $.ajax({
            type: "post",
            url: "/authenticate/",
            dataType: "json",
            data: {
                username: username,
                password: password
            },
            complete: function () {
                $('#btnLogin').removeClass('disabled');
                $('#btnLogin').prop('disabled', false);
            },
            success: function (data) {
                if (data.status === 0) {
                    if (data.data) {
                        document.cookie = "sessionid=" + data.data
                        $(location).attr('href', '/login/2fa/');
                    } else {
                        $(location).attr('href', '/index/');
                    }
                } else {
                    $('#wrongpwd-modal-body').html(data.msg);
                    $('#wrongpwd-modal').modal({
                        keyboard: true
                    });
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alert(errorThrown);
            }
        });
    };
</script>
</html>
