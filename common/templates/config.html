{% extends "base.html" %}
{% load format_tags %}

{% block content %}
    <div class="row clearfix">
        <div class="col-md-3">
            <div class="panel panel-default">
                <div class="panel-heading">
                    选择操作
                </div>
                <div class="panel-body">
                    <div id="div-config" class="form-group">
                        <h5 class="control-label text-bold">配置项：</h5>
                        <div class="form-group">
                            <select id="config" name="confg"
                                    class="selectpicker show-tick form-control bs-select-hidden"
                                    data-name="配置项" data-placeholder="请选择配置项:" required />
                                <option value="is-empty" disabled="">请选择配置项:</option>
                                <option value="0" selected="selected">系统设置</option>
                                <option value="1">工单审核流配置</option>
                            </select>
                        </div>
                    </div>
                    <div id="div-workflow" class="form-group" style="display: none">
                        <h5 class="control-label text-bold">工单类型：</h5>
                        <div class="form-group">
                            <select id="workflow_type" name="group"
                                    class="selectpicker show-tick form-control bs-select-hidden"
                                    data-name="工单类型" data-placeholder="请选择工单类型:" required />
                                {% for c in workflow_choices %}
                                {% if forloop.first %}
                                <option value="{{ c.value }}" selected="selected">
                                {{ c.label }}</option>
                                {% else %}
                                <option value="{{ c.value }}">
                                {{ c.label }}</option>
                                {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                        <h5 class="control-label text-bold">组：</h5>
                        <div class="form-group">
                            <select id="group" name="group"
                                    class="selectpicker show-tick form-control bs-select-hidden"
                                    data-name="组" data-placeholder="请选择组:" data-live-search="true" required />
                                {% for group in group_list %}
                                    <option value="{{ group.group_name }}">{{ group.group_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-9 column">
            <div class="panel panel-default">
                <div class="panel-heading">
                    操作
                </div>
                <div class="panel-body">
                    <div id="div-system-config" class="form-group" style="display: none">
                        <h4 style="color: darkgrey; display: inline;"><b>goInception配置</b></h4>&nbsp;&nbsp;&nbsp;
                        <h6 style="color:red">注1：备份库的信息仅用于平台获取回滚语句，需要和审核工具自身的配置保持一致，此处配置变更不会修改审核工具自身的配置</h6>
                        <hr/>
                        <div class="form-horizontal">
                            <div id="div-go-inception-config">
                                <div class="form-group">
                                    <label for="go_inception_host"
                                           class="col-sm-4 control-label">GO_INCEPTION_HOST</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="go_inception_host"
                                               key="go_inception_host"
                                               value="{{ config.go_inception_host }}"
                                               placeholder="goInception地址" />
                                    </div>
                                    <button id='check_goincption' class='btn-sm btn-info'>测试连接</button>&nbsp;
                                </div>
                                <div class="form-group">
                                    <label for="go_inception_port"
                                           class="col-sm-4 control-label">GO_INCEPTION_PORT</label>
                                    <div class="col-sm-5">
                                        <input type="number" class="form-control"
                                               id="go_inception_port"
                                               key="go_inception_port"
                                               value="{{ config.go_inception_port }}"
                                               placeholder="goInception端口" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="go_inception_user"
                                           class="col-sm-4 control-label">GO_INCEPTION_USER</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="go_inception_user"
                                               key="go_inception_user"
                                               value="{{ config.go_inception_user }}"
                                               placeholder="goInception用户（未启用认证可不填）" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="go_inception_password"
                                           class="col-sm-4 control-label">GO_INCEPTION_PASSWORD</label>
                                    <div class="col-sm-5">
                                        <input type="password" class="form-control"
                                               id="go_inception_password"
                                               key="go_inception_password"
                                               value="{{ config.go_inception_password }}"
                                               placeholder="goInception密码（未启用认证可不填）" />
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inception_remote_backup_host"
                                       class="col-sm-4 control-label">BACKUP_HOST</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control"
                                           id="inception_remote_backup_host"
                                           key="inception_remote_backup_host"
                                           value="{{ config.inception_remote_backup_host }}"
                                           placeholder="备份库地址" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inception_remote_backup_port"
                                       class="col-sm-4 control-label">BACKUP_PORT</label>
                                <div class="col-sm-5">
                                    <input type="number" class="form-control"
                                           id="inception_remote_backup_port"
                                           key="inception_remote_backup_port"
                                           value="{{ config.inception_remote_backup_port }}"
                                           placeholder="备份库端口" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inception_remote_backup_user"
                                       class="col-sm-4 control-label">BACKUP_USER</label>
                                <div class="col-sm-5">
                                    <input type="text" autocomplete="new-password" class="form-control"
                                           id="inception_remote_backup_user"
                                           key="inception_remote_backup_user"
                                           value="{{ config.inception_remote_backup_user }}"
                                           placeholder="备份库用户" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inception_remote_backup_password"
                                       class="col-sm-4 control-label">BACKUP_PASSWORD</label>
                                <div class="col-sm-5">
                                    <input type="password" autocomplete="new-password" class="form-control"
                                           id="inception_remote_backup_password"
                                           key="inception_remote_backup_password"
                                           value="{{ config.inception_remote_backup_password }}"
                                           placeholder="备份库用户密码" />
                                </div>
                            </div>
                        </div>
                        <br>
                        <h4 style="color: darkgrey"><b>功能模块配置</b></h4>
                        <hr/>
                        <h5 style="color: darkgrey"><b>SQL上线</b></h5>
                        <hr/>
                        <div class="form-horizontal">
                            <div class="form-group">
                                <label for="critical_ddl_regex"
                                       class="col-sm-4 control-label">CRITICAL_DDL_REGEX</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control"
                                           id="critical_ddl_regex"
                                           key="critical_ddl_regex"
                                           value="{{ config.critical_ddl_regex }}"
                                           placeholder="正则条件，匹配的语句会禁止提交" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="auto_review_wrong"
                                       class="col-sm-4 control-label">AUTO_REVIEW_WRONG</label>
                                <div class="col-sm-5">
                                    <input type="number" class="form-control"
                                           id="auto_review_wrong"
                                           key="auto_review_wrong"
                                           value="{{ config.auto_review_wrong }}"
                                           placeholder="自动驳回的等级，1表示警告驳回，2和空表示错误才驳回，其他表示不驳回" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="enable_backup_switch"
                                       class="col-sm-4 control-label">ENABLE_BACKUP_SWITCH</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="enable_backup_switch"
                                                   key="enable_backup_switch"
                                                   value="{{ config.enable_backup_switch }}"
                                                   type="checkbox" />
                                            是否开启备份选项(默认关闭, 强制要求备份)
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="auto_review"
                                       class="col-sm-4 control-label">AUTO_REVIEW</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="auto_review"
                                                   key="auto_review"
                                                   value="{{ config.auto_review }}"
                                                   type="checkbox" />
                                            是否开启SQL上线自动审批
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div id="div-auto-review-config" style="display: none">
                                <div class="form-group">
                                    <label for="auto_review_tag"
                                           class="col-sm-4 control-label">AUTO_REVIEW_TAG</label>
                                    <div class="col-sm-5">
                                        <select id="auto_review_tag" key="auto_review_tag"
                                                class="form-control selectpicker" data-live-search="true"
                                                multiple data-selected-text-format="count > 5"
                                                data-none-selected-text="自动审批的实例标签">
                                            {% for tag in instance_tags %}
                                                {% if tag.tag_code|is_in:config.auto_review_tag %}
                                                    <option value="{{ tag.tag_code }}"
                                                            selected>{{ tag.tag_name }}</option>
                                                {% else %}
                                                    <option value="{{ tag.tag_code }}">{{ tag.tag_name }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="auto_review_db_type"
                                           class="col-sm-4 control-label">AUTO_REVIEW_DB_TYPE</label>
                                    <div class="col-sm-5">
                                        <select id="auto_review_db_type" key="auto_review_db_type"
                                                class="form-control selectpicker" data-live-search="true"
                                                multiple data-selected-text-format="count > 5"
                                                data-none-selected-text="自动审批的数据库类型">
                                            {% for type in db_type %}
                                                {% if type|is_in:config.auto_review_db_type %}
                                                    <option value="{{ type }}" selected>{{ type }}</option>
                                                {% else %}
                                                    <option value="{{ type }}">{{ type }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="auto_review_regex"
                                           class="col-sm-4 control-label">AUTO_REVIEW_REGEX</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="auto_review_regex"
                                               key="auto_review_regex"
                                               value="{{ config.auto_review_regex }}"
                                               placeholder="正则条件，匹配的语句需要人工审批" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="auto_review_max_update_rows"
                                           class="col-sm-4 control-label">MAX_UPDATE_ROWS</label>
                                    <div class="col-sm-5">
                                        <input type="number" class="form-control"
                                               id="auto_review_max_update_rows"
                                               key="auto_review_max_update_rows"
                                               value="{{ config.auto_review_max_update_rows }}"
                                               placeholder="自动审批允许工单最大更新行数" />
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="manual"
                                       class="col-sm-4 control-label">MANNUAL</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="manual"
                                                   key="manual"
                                                   value="{{ config.manual }}"
                                                   type="checkbox" />
                                            是否开启SQL上线手工执行确认
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="ddl_dml_separation"
                                       class="col-sm-4 control-label">DDL_DML_SEPARATION</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="ddl_dml_separation"
                                                   key="ddl_dml_separation"
                                                   value="{{ config.ddl_dml_separation }}"
                                                   type="checkbox" />
                                            是否禁止DDL和DML在SQL上线同时提交(MySQL)
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="ban_self_audit"
                                       class="col-sm-4 control-label">BAN_SELF_AUDIT</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <input id="ban_self_audit"
                                                  key="ban_self_audit"
                                                  value="{{ config.ban_self_audit }}"
                                                  type="checkbox" />
                                        <label for="ban_self_audit" title="ON：禁止自审。OFF：允许自己审批自己的工单">禁止自审工单(管理员除外)</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="real_row_count"
                                       class="col-sm-4 control-label">REAL_ROW_COUNT</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="real_row_count"
                                                   key="real_row_count"
                                                   value="{{ config.real_row_count }}"
                                                   type="checkbox">
                                            是否获取DML真实影响行数(MySQL、MongoDB)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h5 style="color: darkgrey"><b>SQL查询</b></h5>
                        <h6 style="color:red">注：开启脱敏功能必须要配置goInception信息，用于SQL语法解析</h6>
                        <hr/>
                        <div class="form-horizontal">
                            <div class="form-group">
                                <label for="data_masking"
                                       class="col-sm-4 control-label">DATA_MASKING</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="data_masking"
                                                   key="data_masking"
                                                   value="{{ config.data_masking }}" type="checkbox" />
                                            是否开启动态脱敏
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="query_check"
                                       class="col-sm-4 control-label">QUERY_CHECK</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="query_check"
                                                   key="query_check"
                                                   value="{{ config.query_check }}" type="checkbox" />
                                            是否开启脱敏校验(无法脱敏的语句会抛错)
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="disable_star"
                                       class="col-sm-4 control-label">DISABLE_STAR</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="disable_star"
                                                   key="disable_star"
                                                   value="{{ config.disable_star }}" type="checkbox" />
                                            禁止在查询时使用 *
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="max_execution_time"
                                       class="col-sm-4 control-label">MAX_EXECUTION_TIME</label>
                                <div class="col-sm-5">
                                    <input type="number" class="form-control" id="max_execution_time"
                                           key="max_execution_time"
                                           value="{{ config.max_execution_time }}"
                                           placeholder="在线查询超时时间阈值，单位秒，默认60" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="admin_query_limit"
                                       class="col-sm-4 control-label">ADMIN_QUERY_LIMIT</label>
                                <div class="col-sm-5">
                                    <input type="number" class="form-control" id="admin_query_limit"
                                           key="admin_query_limit"
                                           value="{{ config.admin_query_limit }}"
                                           placeholder="管理员/DBA查询结果集限制" />
                                </div>
                            </div>
                            <h5 style="color: darkgrey"><b>SQL优化</b></h5>
                            <hr/>
                            <div class="form-group">
                                <label for="sqladvisor"
                                       class="col-sm-4 control-label">SQLADVISOR_PATH</label>
                                <div class="col-sm-5">
                                    <div class="switch switch-small">
                                        <input type="text" class="form-control" id="sqladvisor" key="sqladvisor"
                                               value="{{ config.sqladvisor }}"
                                               placeholder="SQLADVISOR路径" />
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="soar"
                                       class="col-sm-4 control-label">SOAR_PATH</label>
                                <div class="col-sm-5">
                                    <div class="switch switch-small">
                                        <input type="text" class="form-control" id="soar" key="soar"
                                               value="{{ config.soar }}"
                                               placeholder="SOAR路径" />
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="soar_test_dsn"
                                       class="col-sm-4 control-label">SOAR_TEST_DSN</label>
                                <div class="col-sm-5">
                                    <div class="switch switch-small">
                                        <input type="text" class="form-control" id="soar_test_dsn" key="soar_test_dsn"
                                               value="{{ config.soar_test_dsn }}"
                                               placeholder="SOAR测试实例：user:pwd@host:port/db" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <br>
                        <h4 style="color: darkgrey;display: inline"><b>通知配置</b></h4>&nbsp;&nbsp;&nbsp;
                        <h6 style="color:red">注1：钉钉Webhook需要配置资源组Webhook地址，Webhook无法通知到指定用户</h6>
                        <h6 style="color:red">注2：企业微信自建应用开启后，若未配置企业微信UserID，则默认取用户的username作为UserId</h6>
                        <h6 style="color:red">注3：企业微信群机器人需要配置资源组webhook地址（企业微信群机器人是通知到群；企业微信自建应用是通过应用通知到指定人）</h6>
                        <hr/>
                        <div class="form-horizontal">
                        <h5 style="color: darkgrey"><b>工单通知</b></h5>
                            <hr/>
                            <div class="form-group">
                                <label for="archery_base_url"
                                       class="col-sm-4 control-label">ARCHERY_BASE_URL</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control"
                                           id="archery_base_url"
                                           key="archery_base_url"
                                           value="{{ config.archery_base_url }}"
                                           placeholder="系统HOST地址, 用于通知链接，如https://archery.com" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="ddl_notify_auth_group"
                                       class="col-sm-4 control-label">DDL_NOTIFY_AUTH_GROUP</label>
                                <div class="col-sm-5">
                                    <select id="ddl_notify_auth_group" key="ddl_notify_auth_group"
                                            class="selectpicker show-tick form-control bs-select-hidden"
                                            data-live-search="true"
                                            data-none-selected-text="DDL工单通知权限组"
                                            multiple data-selected-text-format="count > 3"
                                            required>
                                        {% for auth_group in auth_group_list %}
                                            {% if auth_group.name|is_in:config.ddl_notify_auth_group %}
                                                <option value="{{ auth_group.name }}"
                                                        selected>{{ auth_group.name }}</option>
                                            {% else %}
                                                <option value="{{ auth_group.name }}">{{ auth_group.name }}</option>
                                            {% endif %}
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="notify_phase_control"
                                       class="col-sm-4 control-label">NOTIFY_PHASE_CONTROL</label>
                                <div class="col-sm-5">
                                    <select id="notify_phase_control" key="notify_phase_control"
                                            class="selectpicker show-tick form-control bs-select-hidden"
                                            data-live-search="true"
                                            data-none-selected-text="工单通知分阶段控制"
                                            multiple required>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="mail"
                                       class="col-sm-4 control-label">MAIL</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="mail" key="mail" value="{{ config.mail }}"
                                                   type="checkbox" /> 是否开启邮件通知
                                        </label>
                                        <button id='check_email' class='btn-sm btn-info'>测试连接</button>
                                    </div>
                                </div>
                            </div>
                            <div id="div-mail-config" style="display: none">
                                <div class="form-group">
                                    <label for="mail_ssl"
                                           class="col-sm-4 control-label">MAIL_SSL</label>
                                    <div class="col-sm-8">
                                        <div class="switch switch-small">
                                            <label>
                                                <input id="mail_ssl" key="mail_ssl"
                                                       value="{{ config.mail_ssl }}"
                                                       type="checkbox" /> 是否使用SSL
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="mail_smtp_server"
                                           class="col-sm-4 control-label">MAIL_SMTP_SERVER</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="mail_smtp_server"
                                               key="mail_smtp_server"
                                               value="{{ config.mail_smtp_server }}"
                                               placeholder="邮件smtp地址" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="mail_smtp_port"
                                           class="col-sm-4 control-label">MAIL_SMTP_PORT</label>
                                    <div class="col-sm-5">
                                        <input type="number" class="form-control"
                                               id="mail_smtp_port"
                                               key="mail_smtp_port"
                                               value="{{ config.mail_smtp_port }}"
                                               placeholder="邮件smtp端口" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="mail_smtp_user"
                                           class="col-sm-4 control-label">MAIL_SMTP_USER</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="mail_smtp_user"
                                               key="mail_smtp_user"
                                               value="{{ config.mail_smtp_user }}"
                                               placeholder="邮箱账号" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="mail_smtp_password"
                                           class="col-sm-4 control-label">MAIL_SMTP_PASSWORD</label>
                                    <div class="col-sm-5">
                                        <input type="password" class="form-control"
                                               id="mail_smtp_password"
                                               key="mail_smtp_password"
                                               value="{{ config.mail_smtp_password }}"
                                               placeholder="邮箱账号密码" />
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="ding"
                                       class="col-sm-4 control-label">DING</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="ding" key="ding" value="{{ config.ding }}"
                                                   type="checkbox" /> 是否开启钉钉Webhook通知
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="ding_to_person"
                                       class="col-sm-4 control-label">DING_TO_PERSON</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="ding_to_person" key="ding_to_person"
                                                   value="{{ config.ding_to_person }}"
                                                   type="checkbox" /> 是否开启钉钉个人消息通知
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div id="div-ding-config" style="display: none">
                                <div class="form-group">
                                    <i class="ace-icon fa fa-hand-o-right"></i>
                                    创建钉钉Archery微应用参考：<a target="_blank"
                                                        href="https://ding-doc.dingtalk.com/doc#/bgb96b/aw3h75  ">钉钉企业接入开发文档</a>
                                    &nbsp;&nbsp;&nbsp;<button id='sync_ding_user' class='btn-xs btn-warning'>同步用户
                                </button>
                                </div>
                                <div class="form-group">
                                    <label for="ding_agent_id"
                                           class="col-sm-4 control-label">AgentId</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="ding_agent_id"
                                               key="ding_agent_id"
                                               value="{{ config.ding_agent_id }}"
                                               placeholder="微应用Archery的AgentId" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="ding_app_key"
                                           class="col-sm-4 control-label">AppKey</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="ding_app_key"
                                               key="ding_app_key"
                                               value="{{ config.ding_app_key }}"
                                               placeholder="微应用Archery的AppKey" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="ding_app_secret"
                                           class="col-sm-4 control-label">AppSecret</label>
                                    <div class="col-sm-5">
                                        <input type="password" class="form-control"
                                               id="ding_app_secret"
                                               key="ding_app_secret"
                                               value="{{ config.ding_app_secret }}"
                                               placeholder="微应用Archery的AppSecret" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="ding_archery_username"
                                           class="col-sm-4 control-label">Archery2Ding</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="ding_archery_username"
                                               key="ding_archery_username"
                                               value="{{ config.ding_archery_username }}"
                                               placeholder="Archery.username对应钉钉里的字段" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="ding_dept_ids"
                                           class="col-sm-4 control-label">DingDeptIDs</label>
                                    <div class="col-sm-5">
                                        <textarea class="form-control"
                                                  id="ding_dept_ids"
                                                  key="ding_dept_ids"
                                                  placeholder="钉钉部门id，使用逗号分隔">{{ config.ding_dept_ids }}</textarea />
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="wx"
                                       class="col-sm-4 control-label">企业微信自建应用</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="wx" key="wx"
                                                   value="{{ config.wx }}"
                                                   type="checkbox" /> 是否开启企业微信自建应用通知
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div id="div-wx-config" style="display: none">
                                <div class="form-group">
                                    <i class="ace-icon fa fa-hand-o-right"></i>
                                    创建企业微信应用参考：<a target="_blank"
                                                  href="https://work.weixin.qq.com/api/doc#90000/90135/90226">企业微信接入开发文档</a>
                                    {#                                    &nbsp;&nbsp;&nbsp;<button id='sync_ding_user' class='btn-xs btn-warning'>同步用户#}
                                    </button>
                                </div>
                                <div class="form-group">
                                    <label for="wx_corpid"
                                           class="col-sm-4 control-label">CorpId</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="wx_corpid"
                                               key="wx_corpid"
                                               value="{{ config.wx_corpid }}"
                                               placeholder="企业微信的企业ID" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="wx_agent_id"
                                           class="col-sm-4 control-label">AgentId</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="wx_agent_id"
                                               key="wx_agent_id"
                                               value="{{ config.wx_agent_id }}"
                                               placeholder="应用的AgentId" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="wx_app_secret"
                                           class="col-sm-4 control-label">Secret</label>
                                    <div class="col-sm-5">
                                        <input type="password" class="form-control"
                                               id="wx_app_secret"
                                               key="wx_app_secret"
                                               value="{{ config.wx_app_secret }}"
                                               placeholder="应用的Secret" />
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="qywx_webhook"
                                       class="col-sm-4 control-label">企业微信群机器人</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="qywx_webhook" key="qywx_webhook"
                                                   value="{{ config.qywx_webhook }}"
                                                   type="checkbox" /> 是否开启企业微信群机器人通知
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="feishu_webhook"
                                       class="col-sm-4 control-label">飞书Webhook</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="feishu_webhook" key="feishu_webhook"
                                                   value="{{ config.feishu_webhook }}"
                                                   type="checkbox" /> 是否开启飞书Webhook通知
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="feishu"
                                       class="col-sm-4 control-label">飞书</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="feishu" key="feishu"
                                                   value="{{ config.feishu }}"
                                                   type="checkbox" /> 是否开启飞书通知
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div id="div-feishu-config" style="display: none">
                                <div class="form-group">
                                    <i class="ace-icon fa fa-hand-o-right"></i>
                                    创建飞书应用参考：<a target="_blank"
                                                href="https://open.feishu.cn/document/uQjL04CN/ukzM04SOzQjL5MDN">飞书接入开发文档</a>
                                    {#                                    &nbsp;&nbsp;&nbsp;<button id='sync_ding_user' class='btn-xs btn-warning'>同步用户#}
                                    </button>
                                </div>
                                <div class="form-group">
                                    <label for="feishu_appid"
                                           class="col-sm-4 control-label">飞书应用AppID</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="feishu_appid"
                                               key="feishu_appid"
                                               value="{{ config.feishu_appid }}"
                                               placeholder="飞书应用AppID" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="feishu_app_secret"
                                           class="col-sm-4 control-label">飞书AppSecret</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="feishu_app_secret"
                                               key="feishu_app_secret"
                                               value="{{ config.feishu_app_secret }}"
                                               placeholder="飞书AppSecret" />
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="generic_webhook_url"
                                       class="col-sm-4 control-label">通用Webhook地址</label>
                                <div class="col-sm-5">
                                    <input class="form-control"
                                           id="generic_webhook_url"
                                           key="generic_webhook_url"
                                           value="{{ config.generic_webhook_url }}"
                                           placeholder="通用 webhook 地址, 每个工单的每个状态转变均会调用" />
                                </div>
                            </div>
                            <h5 style="color: darkgrey"><b>短信服务</b></h5>
                            <hr/>
                            <div class="form-group">
                                <label for="sms_provider"
                                       class="col-sm-4 control-label">短信服务提供商</label>
                                <div class="col-sm-5">
                                    <select id="sms_provider" key="sms_provider"
                                            class="selectpicker show-tick form-control bs-select-hidden"
                                            data-live-search="true"
                                            data-none-selected-text="短信服务商"
                                            required>
                                    </select>
                                </div>
                            </div>
                            <div id="aliyun_sms" style="display: none">
                                <div class="form-group">
                                    <label for="aliyun_access_key_id"
                                           class="col-sm-4 control-label">ALIYUN_ACCESS_KEY_ID</label>
                                    <div class="col-sm-5">
                                        <input type="password" class="form-control"
                                               id="aliyun_access_key_id"
                                               key="aliyun_access_key_id"
                                               value="{{ config.aliyun_access_key_id }}"
                                               placeholder="阿里云访问密钥ID，用于标识用户" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="aliyun_access_key_secret"
                                           class="col-sm-4 control-label">ALIYUN_ACCESS_KEY_SECRET</label>
                                    <div class="col-sm-5">
                                        <input type="password" class="form-control"
                                               id="aliyun_access_key_secret"
                                               key="aliyun_access_key_secret"
                                               value="{{ config.aliyun_access_key_secret }}"
                                               placeholder="阿里云访问密钥（AccessKey），用于验证用户的密钥" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="aliyun_sign_name"
                                           class="col-sm-4 control-label">ALIYUN_SIGN_NAME</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="aliyun_sign_name"
                                               key="aliyun_sign_name"
                                               value="{{ config.aliyun_sign_name }}"
                                               placeholder="短信签名，需为审核通过状态" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="aliyun_template_code"
                                           class="col-sm-4 control-label">ALIYUN_TEMPLATE_CODE</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="aliyun_template_code"
                                               key="aliyun_template_code"
                                               value="{{ config.aliyun_template_code }}"
                                               placeholder="短信模板Code，需为审核通过状态" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="aliyun_variable_name"
                                           class="col-sm-4 control-label">ALIYUN_VARIABLE_NAME</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="aliyun_variable_name"
                                               key="aliyun_variable_name"
                                               value="{{ config.aliyun_variable_name }}"
                                               placeholder="短信模板中的变量名，如：code" />
                                    </div>
                                </div>
                            </div>
                            <div id="tencent_sms" style="display: none">
                                <div class="form-group">
                                    <label for="tencent_secret_id"
                                           class="col-sm-4 control-label">TENCENT_SECRET_ID</label>
                                    <div class="col-sm-5">
                                        <input type="password" class="form-control"
                                               id="tencent_secret_id"
                                               key="tencent_secret_id"
                                               value="{{ config.tencent_secret_id }}"
                                               placeholder="用于标识API调用者的身份" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="tencent_secret_key"
                                           class="col-sm-4 control-label">TENCENT_SECRET_KEY</label>
                                    <div class="col-sm-5">
                                        <input type="password" class="form-control"
                                               id="tencent_secret_key"
                                               key="tencent_secret_key"
                                               value="{{ config.tencent_secret_key }}"
                                               placeholder="用于加密签名字符串和服务器端验证签名字符串的密钥" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="tencent_sign_name"
                                           class="col-sm-4 control-label">TENCENT_SIGN_NAME</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="tencent_sign_name"
                                               key="tencent_sign_name"
                                               value="{{ config.tencent_sign_name }}"
                                               placeholder="短信签名，需为审核通过状态" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="tencent_template_id"
                                           class="col-sm-4 control-label">TENCENT_TEMPLATE_ID</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="tencent_template_id"
                                               key="tencent_template_id"
                                               value="{{ config.tencent_template_id }}"
                                               placeholder="短信模板ID，需为审核通过状态" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="tencent_sdk_appid"
                                           class="col-sm-4 control-label">TENCENT_SDK_APPID</label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control"
                                               id="tencent_sdk_appid"
                                               key="tencent_sdk_appid"
                                               value="{{ config.tencent_sdk_appid }}"
                                               placeholder="短信应用ID" />
                                    </div>
                                </div>
                            </div>
                        <br>
                        </div>

                        <h4 style="color: darkgrey; display: inline;"><b>OIDC 配置</b></h4>
                        <hr/>

                        <div class="form-horizontal">
                            <div class="form-group">
                                <label for="index_path_url"
                                       class="col-sm-4 control-label">OIDC_BTN_NAME</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control"
                                           id="oidc_btn_name"
                                           key="oidc_btn_name"
                                           value="{{ config.oidc_btn_name }}"
                                           placeholder="登录按钮显示名称，比如Login With KeyCloak" />
                                </div>
                            </div>
                        </div>

                        <h4 style="color: darkgrey; display: inline;"><b>飞书 OAuth 配置</b></h4>
                        <hr/>
                        <div class="form-horizontal">
                            <div class="form-group">
                                <label for="feishu_btn_name"
                                       class="col-sm-4 control-label">FEISHU_BTN_NAME</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control"
                                           id="feishu_btn_name"
                                           key="feishu_btn_name"
                                           value="{{ config.feishu_btn_name }}"
                                           placeholder="登录按钮显示名称，比如以飞书登录" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="feishu_oauth_app_id"
                                       class="col-sm-4 control-label">FEISHU_OAUTH_APP_ID</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control"
                                           id="feishu_oauth_app_id"
                                           key="feishu_oauth_app_id"
                                           value="{{ config.feishu_oauth_app_id }}"
                                           placeholder="飞书应用的 App ID" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="feishu_oauth_app_secret"
                                       class="col-sm-4 control-label">FEISHU_OAUTH_APP_SECRET</label>
                                <div class="col-sm-5">
                                    <input type="password" class="form-control"
                                           id="feishu_oauth_app_secret"
                                           key="feishu_oauth_app_secret"
                                           value="{{ config.feishu_oauth_app_secret }}"
                                           placeholder="飞书应用的 App Secret" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="feishu_oauth_redirect_uri"
                                       class="col-sm-4 control-label">FEISHU_OAUTH_REDIRECT_URI</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control"
                                           id="feishu_oauth_redirect_uri"
                                           key="feishu_oauth_redirect_uri"
                                           value="{{ config.feishu_oauth_redirect_uri }}"
                                           placeholder="飞书 OAuth 回调地址，如 https://your-domain.com/feishu/callback/" />
                                </div>
                            </div>
                        </div>

                        <h4 style="color: darkgrey; display: inline;"><b>OPENAI 配置</b></h4>
                        <h6 style="color:red">注1：若无OPENAI_API_KEY配置，则不开启相关功能</h6>
                        <h6 style="color:red">注2：DEFAULT_CHAT_MODEL 默认配置为gpt-3.5-turbo，DEFAULT_QUERY_TEMPLATE 默认配置为系统定义的模板</h6>
                        <hr/>
                        <div class="form-horizontal">
                            <div class="form-group">
                                <label for="openai_base_url"
                                       class="col-sm-4 control-label">OPENAI_BASE_URL</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control" id="openai_base_url"
                                           key="openai_base_url"
                                           value="{{ config.openai_base_url }}"
                                           placeholder="openai base url" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="openai_api_key"
                                       class="col-sm-4 control-label">OPENAI_API_KEY</label>
                                <div class="col-sm-5">
                                    <input type="password" class="form-control" id="openai_api_key"
                                           key="openai_api_key"
                                           value="{{ config.openai_api_key }}"
                                           placeholder="openai api key" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="default_chat_model"
                                       class="col-sm-4 control-label">DEFAULT_CHAT_MODEL</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control" id="default_chat_model"
                                           key="default_chat_model"
                                           value="{{ config.default_chat_model }}"
                                           placeholder="openai default chat model" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="default_query_template"
                                       class="col-sm-4 control-label">DEFAULT_QUERY_TEMPLATE</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control" id="default_query_template"
                                           key="default_query_template"
                                           value="{{ config.default_query_template }}"
                                           placeholder="默认生成SQL语句的Django模板, 无模板会导致生成结果失败, 需提供db_type/table_schema/user_input" />
                                </div>
                            </div>
                        </div>

                        <h4 style="color: darkgrey"><b>其他配置</b></h4>
                        <hr/>
                        <div class="form-horizontal">
                            <div class="form-group">
                                <label for="index_path_url"
                                       class="col-sm-4 control-label">INDEX_PATH_URL</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control"
                                           id="index_path_url"
                                           key="index_path_url"
                                           value="{{ config.index_path_url }}"
                                           placeholder="系统首页路径，默认SQL工单页面：sqlworkflow" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="my2sql"
                                       class="col-sm-4 control-label">MY2SQL</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control"
                                           id="my2sql"
                                           key="my2sql"
                                           value="{{ config.my2sql }}"
                                           placeholder="my2sql调用路径，类似/opt/archery/src/plugins/my2sql" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="default_auth_group"
                                       class="col-sm-4 control-label">DEFAULT_AUTH_GROUP</label>
                                <div class="col-sm-5">
                                    <select id="default_auth_group" key="default_auth_group"
                                            class="selectpicker show-tick form-control bs-select-hidden"
                                            data-live-search="true"
                                            data-none-selected-text="新用户首次登录自动关联权限组"
                                            multiple data-selected-text-format="count > 3"
                                            required>
                                        {% for auth_group in auth_group_list %}
                                            {% if auth_group.name|is_in:config.default_auth_group %}
                                                <option value="{{ auth_group.name }}"
                                                        selected>{{ auth_group.name }}</option>
                                            {% else %}
                                                <option value="{{ auth_group.name }}">{{ auth_group.name }}</option>
                                            {% endif %}
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="default_resource_group"
                                       class="col-sm-4 control-label">DEFAULT_RESOURCE_GROUP</label>
                                <div class="col-sm-5">
                                    <select id="default_resource_group" key="default_resource_group"
                                            class="selectpicker show-tick form-control bs-select-hidden"
                                            data-live-search="true"
                                            data-none-selected-text="新用户首次登录自动关联资源组"
                                            multiple data-selected-text-format="count > 3"
                                            required>
                                        {% for resource_group in group_list %}
                                            {% if resource_group.group_name|is_in:config.default_resource_group %}
                                                <option value="{{ resource_group.group_name }}"
                                                        selected>{{ resource_group.group_name }}</option>
                                            {% else %}
                                                <option value="{{ resource_group.group_name }}">{{ resource_group.group_name }}</option>
                                            {% endif %}
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="api_user_whitelist"
                                       class="col-sm-4 control-label">API_USER_WHITELIST</label>
                                <div class="col-sm-5">
                                    <select id="api_user_whitelist" key="api_user_whitelist"
                                            class="selectpicker show-tick form-control bs-select-hidden"
                                            data-live-search="true"
                                            data-none-selected-text="API用户白名单"
                                            multiple data-selected-text-format="count > 5"
                                            required>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="lock_time_threshold"
                                       class="col-sm-4 control-label">LOCK_TIME_THRESHOLD</label>
                                <div class="col-sm-5">
                                    <input type="number" class="form-control"
                                           id="lock_time_threshold"
                                           key="lock_time_threshold"
                                           value="{{ config.lock_time_threshold }}"
                                           placeholder="账户登录失败锁定时间(秒)" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="lock_cnt_threshold"
                                       class="col-sm-4 control-label">LOCK_CNT_THRESHOLD</label>
                                <div class="col-sm-5">
                                    <input type="number" class="form-control"
                                           id="lock_cnt_threshold"
                                           key="lock_cnt_threshold"
                                           value="{{ config.lock_cnt_threshold }}"
                                           placeholder="账户登录失败几次锁账户" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="sign_up_enabled"
                                       class="col-sm-4 control-label">SIGN_UP_ENABLED</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="sign_up_enabled" key="sign_up_enabled"
                                                   value="{{ config.sign_up_enabled }}"
                                                   type="checkbox" /> 是否开启注册功能
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="watermark_enabled"
                                       class="col-sm-4 control-label">WATERMARK_ENABLED</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="watermark_enabled" key="watermark_enabled"
                                                   value="{{ config.watermark_enabled }}"
                                                   type="checkbox" /> 是否开启水印功能
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="enforce_2fa"
                                       class="col-sm-4 control-label">ENFORCE_2FA</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="enforce_2fa" key="enforce_2fa"
                                                   value="{{ config.enforce_2fa }}"
                                                   type="checkbox" /> 登录是否强制使用2FA
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="announcement_content_enabled"
                                       class="col-sm-4 control-label">ANNOUNCEMENT_CONTENT_ENABLED</label>
                                <div class="col-sm-8">
                                    <div class="switch switch-small">
                                        <label>
                                            <input id="announcement_content_enabled" key="announcement_content_enabled"
                                                   value="{{ config.announcement_content_enabled }}"
                                                   type="checkbox"> 是否开启公告
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="announcement_content"
                                       class="col-sm-4 control-label">ANNOUNCEMENT_CONTENT</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control"
                                           id="announcement_content"
                                           key="announcement_content"
                                           value="{{ config.announcement_content }}"
                                           placeholder="公告内容">
                                            
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="custom_title_suffix"
                                       class="col-sm-4 control-label">CUSTOM_TITLE_SUFFIX</label>
                                <div class="col-sm-5">
                                    <input type="text" class="form-control"
                                           id="custom_title_suffix"
                                           key="custom_title_suffix"
                                           value="{{ config.custom_title_suffix }}"
                                           placeholder="在网站标题及登录页面追加此内容, 可用于多archery实例的区分">
                                </div>
                            </div>
                        </div>
                        <hr />
                        <div class="form-group">
                            <div class="col-sm-offset-4 col-sm-10">
                                <button id="saveconfig" type="button" class="btn btn-primary">保存配置</button>
                            </div>
                        </div>
                    </div>
                    <div id="div-workflow-config" class="form-inline form-group" style="display: none">
                        <h5 class="control-label text-bold" style="color: red">
                            规则：<b>选择多个权限组即审批流程为多级审核，按照选择顺序进行流转，权限组内用户都可审核</b></h5>
                        <br>
                        <h5 class="control-label text-bold">当前审批流程：<b id="workflow_auditors"></b></h5>
                        <br>
                        <div><h5 style="float:left;" class="control-label text-bold">变更审批流程：</h5></div>
                        <div class="form-group form-inline">
                            <div class="form-group">
                                <input type="text" class="form-control" id="workflow_auditors_text"
                                       disabled="disabled" style="width: 283px">
                            </div>
                            <div class="form-group">
                                <select id="group_auditors" name="group" title="请选择审批权限组"
                                        class="selectpicker show-tick form-control bs-select-hidden"
                                        data-name="审批人" data-placeholder="审批人:" data-live-search="true"
                                        required>
                                    {% for auth_group in auth_group_list %}
                                        <option value="{{ auth_group.name }}">{{ auth_group.name }}
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <button id="btn-workflow-config" class="btn btn-default">变更</button>
                            <button id="btn-workflow-config-clean" class="btn btn-default">刷新</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block js %}
    {% load static %}
    <link href="{% static 'bootstrap-switch/css/bootstrap-switch.min.css' %}" rel="stylesheet" type="text/css"/>
    <script src="{% static 'bootstrap-switch/js/bootstrap-switch.min.js' %}"></script>
    <script>

        $(document).ready(function (){
            // notify_phase_control参数处理
            let cfg_value = "{{ config.notify_phase_control }}";
            let phases = ['Apply', 'Pass', 'Execute', 'Cancel'];
            // notify_phase_control未设置时默认全部开启
            if (cfg_value === "") {
                for (let i in phases) {
                    let phase = "<option value=\"" + phases[i]+ "\" selected>" + phases[i] + "</option>"
                    $("#notify_phase_control").append(phase)
                }
            } else {
                for (let i=0;i<phases.length;i++) {
                    let phase = "";
                    let cfg_arr = cfg_value.split(',');
                    if (cfg_arr.indexOf(phases[i]) >= 0) {
                        phase = "<option value=\"" + phases[i]+ "\" selected>" + phases[i] + "</option>"
                    } else {
                        phase = "<option value=\"" + phases[i]+ "\">" + phases[i] + "</option>"
                    }
                    $("#notify_phase_control").append(phase)
                }
            }
            $("#notify_phase_control").selectpicker('render');
            $("#notify_phase_control").selectpicker('refresh');

            // api_user_whitelist参数处理
            let api_config = "{{ config.api_user_whitelist }}";
            $.ajax({
                type: "post",
                url: "/user/list/",
                dataType: "json",
                success: function (data) {
                    if (data.status === 0) {
                        let users = data.data;
                        let user_arr = api_config.split(',');
                        for (let i=0;i<users.length;i++) {
                            let user = "";
                            if (user_arr.indexOf(users[i].id.toString()) >= 0) {
                                user = "<option value=\"" + users[i].id+ "\" selected>" + users[i].display + "(" + users[i].username + ")</option>"
                            } else {
                                user = "<option value=\"" + users[i].id+ "\">" + users[i].display + "(" + users[i].username + ")</option>"
                            }
                            $("#api_user_whitelist").append(user)
                            $("#api_user_whitelist").selectpicker('render');
                            $("#api_user_whitelist").selectpicker('refresh');
                        }
                    } else {
                        alert(data.msg);
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert(errorThrown);
                }
            });

            // sms_provider参数处理
            function provider_display(provider) {
                if (provider === 'disabled') {
                    return '关闭'
                } else if (provider === 'aliyun') {
                    return '阿里云'
                } else if (provider === 'tencent') {
                    return '腾讯云'
                }
            }
            let sms_provider = "{{ config.sms_provider }}";
            if (sms_provider === 'disabled' || sms_provider === '') {
                $("#aliyun_sms").hide();
                $("#tencent_sms").hide();
            } else if (sms_provider === 'aliyun') {
                $("#aliyun_sms").show();
                $("#tencent_sms").hide();
            } else if (sms_provider === 'tencent') {
                $("#aliyun_sms").hide();
                $("#tencent_sms").show();
            }
            let providers = ['disabled', 'aliyun', 'tencent'];
            for (let i=0;i<providers.length;i++) {
                let provider;
                if (providers[i] === sms_provider) {
                    provider = "<option value=\"" + providers[i]+ "\" selected>" + provider_display(providers[i]) + "</option>"
                } else {
                    provider = "<option value=\"" + providers[i]+ "\">" + provider_display(providers[i]) + "</option>"
                }
                $("#sms_provider").append(provider)
            }
        })

        //配置项切换
        $("#config").change(function () {
            sessionStorage.setItem('config_type', $("#config").val());
            if ($("#config").val() === '0') {
                $("#div-system-config").show();
                $("#div-workflow").hide();
                $("#div-workflow-config").hide();
                $("#div-system-config").find('input[type="checkbox"]').each(function (i) {
                    if ($(this).val() === 'true') {
                        $(this).bootstrapSwitch('state', true);
                    } else {
                        $(this).bootstrapSwitch('state', false);
                    }
                });
            } else if ($("#config").val() === '1') {
                $("#div-system-config").hide();
                $("#div-workflow").show();
                $("#div-workflow-config").show();
                $("#workflow_type").trigger("change");
            }
        });

        // 系统设置checkbox事件
        $('input[type="checkbox"]').on('switchChange.bootstrapSwitch', function (event, state) {
            let id = $(this).attr("id");
            if (state) {
                $(this).val(true);
                if (id === 'mail') {
                    $("#div-mail-config").show();
                } else if (id === 'ding_to_person') {
                    $("#div-ding-config").show();
                } else if (id === 'auto_review') {
                    $("#div-auto-review-config").show();
                } else if (id === 'wx') {
                    $("#div-wx-config").show();
                } else if (id === 'feishu') {
                    $("#div-feishu-config").show();
                } else if (id === 'data_masking') {
                    $("#div-inception-config").show();
                }
            } else {
                $(this).val(false);
                if (id === 'mail') {
                    $("#mail_ssl").val(false);
                    $("#div-mail-config").hide();
                } else if (id === 'ding_to_person') {
                    $("#div-ding-config").hide();
                } else if (id === 'auto_review') {
                    $("#div-auto-review-config").hide();
                } else if (id === 'wx') {
                    $("#div-wx-config").hide();
                } else if (id === 'feishu') {
                    $("#div-feishu-config").hide();
                } else if (id === 'data_masking') {
                    $("#div-inception-config").hide();
                }
            }
        });

        // 修改系统设置
        $("#saveconfig").click(function () {
            // 当开启钉钉通知到个人时，企业id和secret等必须填写
            if ($("#ding_to_person").val() == 'true') {
                var v1 = $("#ding_agent_id").val();
                var v2 = $("#ding_app_key").val();
                var v3 = $("#ding_app_secret").val();
                var v4 = $("#ding_dept_ids").val();
                var v5 = $("#ding_archery_username").val();
                if (v1 == "" || v2 == "" || v3 == "" || v4 == "" | v5 == "") {
                    alert("当开启钉钉通知到个人时，企业id和secret等必须填写!");
                    return;
                }
            }
            var sys_config = $("#div-system-config");
            var configs = [];
            sys_config.find('[key]').each(
                function () {
                    var config_item = $(this).attr("key");
                    var config_value = $(this).val();
                    if (Array.isArray(config_value)) {
                        config_value = config_value.join(',')
                    }
                    configs.push({
                        key: config_item,
                        value: config_value
                    });
                }
            );

            $.ajax({
                type: "post",
                url: "/config/change/",
                dataType: "json",
                data: {
                    configs: JSON.stringify(configs)
                },
                complete: function () {
                },
                success: function (data) {
                    if (data.status === 0) {
                        window.location.reload()
                    } else {
                        alert(data.msg);
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert(errorThrown);
                }
            });
        });

        // 切换组触发工单类型切换事件
        $("#group").change(function () {
            $("#workflow_type").trigger('change')
        });

        // 点击用户填充到workflow_auditors_text
        $("#group_auditors").change(function () {
            var auth_group = $(this).find(':selected').attr("disabled", "disabled").val();
            var auditors = $("#workflow_auditors_text").val();
            if (auditors) {
                $("#workflow_auditors_text").val(auditors + '->' + auth_group);
            } else {
                $("#workflow_auditors_text").val(auth_group)
            }
            $('#group_auditors').selectpicker('render');
            $('#group_auditors').selectpicker('refresh');
        });

        // 重置权限组选择框状态
        $("#group_auditors").on("reset", function () {
            $('#group_auditors').selectpicker('val', []);
            $(this).children().removeAttr("disabled");
            $(this).selectpicker("refresh");
        });

        // 清空审核人信息
        $("#btn-workflow-config-clean").click(function () {
            $('#group_auditors').trigger("reset");
            $("#workflow_auditors_text").val('');
        });

        // 切换工单类型获取对应组负责人
        $("#workflow_type").change(function () {
            $("#div-workflow-config").show();
            $('#group_auditors').trigger("reset");
            $("#workflow_auditors_text").val('');
            if ($("#group").val()) {
                $.ajax({
                    type: "post",
                    url: "/group/auditors/",
                    dataType: "json",
                    data: {
                        group_name: $("#group").val(),
                        workflow_type: $("#workflow_type").val()
                    },
                    complete: function () {

                    },
                    success: function (data) {
                        if (data.status === 0) {
                            var result = data.data;
                            $("#workflow_auditors").text(result['auditors_display']);
                        } else {
                            alert(data.msg);
                        }
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alert(errorThrown);
                    }
                });
            }
        });

        // 变更组工单审批流程
        $("#btn-workflow-config").click(function () {
            if ($("#group").val() && $("#workflow_type").val() && $("#workflow_auditors_text").val()) {
                $(this).addClass('disabled');
                $(this).prop('disabled', true);
                var audit_auth_groups = $("#workflow_auditors_text").val().replace(/->/g, ",");
                $.ajax({
                    type: "post",
                    url: "/group/changeauditors/",
                    dataType: "json",
                    data: {
                        group_name: $("#group").val(),
                        audit_auth_groups: audit_auth_groups,
                        workflow_type: $("#workflow_type").val()
                    },
                    complete: function () {
                        $("#btn-workflow-config").removeClass('disabled');
                        $("#btn-workflow-config").prop('disabled', false);
                    },
                    success: function (data) {
                        if (data.status === 0) {
                            //alert('修改成功');
                            $("#workflow_type").trigger("change")
                        } else {
                            alert(data.msg);
                        }
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alert(errorThrown);
                    }
                });
            } else {
                alert('请选择组、工单类型和审批流程！')
            }

        });

        // 检测inception
        $("#check_incption").click(function () {
            $(this).addClass('disabled');
            $(this).prop('disabled', true);
            $.ajax({
                type: "post",
                url: "/check/inception/",
                dataType: "json",
                data: {
                    inception_host: $("#inception_host").val(),
                    inception_port: $("#inception_port").val(),
                    inception_remote_backup_host: $("#inception_remote_backup_host").val(),
                    inception_remote_backup_port: $("#inception_remote_backup_port").val(),
                    inception_remote_backup_user: $("#inception_remote_backup_user").val(),
                    inception_remote_backup_password: $("#inception_remote_backup_password").val(),
                },
                complete: function () {
                    $("#check_incption").removeClass('disabled');
                    $("#check_incption").prop('disabled', false);
                },
                success: function (data) {
                    if (data.status === 0) {
                        alert('Inception测试连接成功，请点击页面下方的保存按钮生效！')
                    } else {
                        alert(data.msg);
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert(errorThrown);
                }
            });
        });

        // 检测goInception
        $("#check_goincption").click(function () {
            $(this).addClass('disabled');
            $(this).prop('disabled', true);
            $.ajax({
                type: "post",
                url: "/check/go_inception/",
                dataType: "json",
                data: {
                    go_inception_host: $("#go_inception_host").val(),
                    go_inception_port: $("#go_inception_port").val(),
                    go_inception_user: $("#go_inception_user").val(),
                    go_inception_password: $("#go_inception_password").val(),
                    inception_remote_backup_host: $("#inception_remote_backup_host").val(),
                    inception_remote_backup_port: $("#inception_remote_backup_port").val(),
                    inception_remote_backup_user: $("#inception_remote_backup_user").val(),
                    inception_remote_backup_password: $("#inception_remote_backup_password").val(),
                },
                complete: function () {
                    $("#check_goincption").removeClass('disabled');
                    $("#check_goincption").prop('disabled', false);
                },
                success: function (data) {
                    if (data.status === 0) {
                        alert('goInception和备份库测试连接成功，请点击页面下方的保存按钮生效！')
                    } else {
                        alert(data.msg);
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert(errorThrown);
                }
            });
        });

        // 检测邮件
        $("#check_email").click(function () {
            $(this).addClass('disabled');
            $(this).prop('disabled', true);
            $.ajax({
                type: "post",
                url: "/check/email/",
                dataType: "json",
                data: {
                    mail: $("#mail").val(),
                    mail_ssl: $("#mail_ssl").val(),
                    mail_smtp_server: $("#mail_smtp_server").val(),
                    mail_smtp_port: $("#mail_smtp_port").val(),
                    mail_smtp_user: $("#mail_smtp_user").val(),
                    mail_smtp_password: $("#mail_smtp_password").val(),
                },
                complete: function () {
                    $("#check_email").removeClass('disabled');
                    $("#check_email").prop('disabled', false);
                },
                success: function (data) {
                    if (data.status === 0) {
                        alert('测试邮件已成功发送给当前用户，注意查收，请点击页面下方的保存按钮生效！')
                    } else {
                        alert(data.msg);
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert(errorThrown);
                }
            });
        });

        // 同步钉钉用户信息
        $("#sync_ding_user").click(function () {
            // 先检查配置信息
            var v1 = $("#ding_agent_id").val();
            var v2 = $("#ding_app_key").val();
            var v3 = $("#ding_app_secret").val();
            var v4 = $("#ding_dept_ids").val();
            var v5 = $("#ding_archery_username").val();
            if (v1 == "" || v2 == "" || v3 == "" || v4 == "" | v5 == "") {
                alert("请先保存相关配置信息！");
                return;
            }
            $(this).addClass('disabled');
            $(this).prop('disabled', true);
            $.ajax({
                type: "post",
                url: "/4admin/sync_ding_user/",
                dataType: "json",
                data: {},
                complete: function () {
                    $("#sync_ding_user").removeClass('disabled');
                    $("#sync_ding_user").prop('disabled', false);
                },
                success: function (data) {
                    if (data.status === 0) {
                        alert('已添加定时同步任务并且触发同步，点击确认查看任务状态');
                        window.open('/admin/django_q/schedule/?q=sync_ding_user_id')
                    } else {
                        alert(data.msg);
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert(errorThrown);
                }
            });
        });

        // 短信服务商切换
        $("#sms_provider").change(function () {
            let provider = this.value;
            if (provider === 'disabled') {
                $("#aliyun_sms").hide();
                $("#tencent_sms").hide();
            } else if (provider === 'aliyun') {
                $("#aliyun_sms").show();
                $("#tencent_sms").hide();
            } else if (provider === 'tencent') {
                $("#aliyun_sms").hide();
                $("#tencent_sms").show();
            }
        })

        //自动填充操作项
        $(document).ready(function () {
            if (sessionStorage.getItem('config_type')) {
                $("#config").val(sessionStorage.getItem('config_type')).trigger("change")
            } else if ($("#config").val()) {
                $("#config").trigger("change")
            }
        })
    </script>
{% endblock %}
