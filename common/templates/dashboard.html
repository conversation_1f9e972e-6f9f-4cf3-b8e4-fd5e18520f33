{% extends "base.html" %}

{% load cache %}

{% block content %}
    {% cache 600 dashboard %}
        <!-- /.row -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-check fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge">{{ count_stats.sql_wf_cnt }}</div>
                                <div>SQL上线工单</div>
                            </div>
                        </div>
                    </div>
                    <a href="/sqlworkflow/">
                        <div class="panel-footer">
                            <span class="pull-left">更多</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-green">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-search fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge">{{ count_stats.query_wf_cnt }}</div>
                                <div>SQL查询工单</div>
                            </div>
                        </div>
                    </div>
                    <a href="/queryapplylist/">
                        <div class="panel-footer">
                            <span class="pull-left">更多</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-yellow">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-user fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge">{{ count_stats.user_cnt }}</div>
                                <div>有效平台用户</div>
                            </div>
                        </div>
                    </div>
                    <a href="/admin/sql/users/">
                        <div class="panel-footer">
                            <span class="pull-left">更多</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-red">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-database fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge">{{ count_stats.ins_cnt }}</div>
                                <div>实例数量</div>
                            </div>
                        </div>
                    </div>
                    <a href="/instance/">
                        <div class="panel-footer">
                            <span class="pull-left">更多</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <i class="fa fa-bar-chart-o fa-fw"></i> 数据库实例类型统计
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-6">
                                <div>{{ instance_chart.bar4|safe }}</div>
                            </div>
                            <div class="col-lg-6">
                                <div>{{ instance_chart.pie6|safe }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </div>    
        <hr>
        <div id="toolbar" class="form-inline">
            <div class='form-group'>
                <div id="reservation" class="form-control"
                    style="background: #fff; cursor: pointer; padding: 5px 10px; border: 1px solid #ccc; width: 100%">
                    统计时间范围：<i class="fa fa-calendar"></i>&nbsp;
                    <span></span> <i class="fa fa-caret-down"></i>
                </div>
            </div>
        </div>
        <br>
        <!-- /.row -->
        <div class="row">
            <div class="col-lg-8">
                <!-- /.panel -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <i class="fa fa-bar-chart-o fa-fw"></i> SQL查询统计
                    </div>
                    <!-- /.panel-heading -->
                    <div id="chart-line1" class="panel-body">
                        {{ chart.line1|safe }}
                    </div>
                    <!-- /.panel-body -->
                </div>
                <!-- /.panel -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <i class="fa fa-bar-chart-o fa-fw"></i> SQL上线数量
                    </div>
                    <!-- /.panel-heading -->
                    <div id="chart-bar1" class="panel-body">
                        {{ chart.bar1|safe }}
                        <!-- /.row -->
                    </div>
                    <!-- /.panel-body -->
                </div>
                <!-- /.panel -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <i class="fa fa-bar-chart-o fa-fw"></i> SQL上线用户
                    </div>
                    <!-- /.panel-heading -->
                    <div id="chart-bar2" class="panel-body">
                        {{ chart.bar2|safe }}
                    </div>
                    <!-- /.panel-body -->
                </div>
                <!-- /.panel -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <i class="fa fa-bar-chart-o fa-fw"></i> SQL上线工单
                    </div>
                    <!-- /.panel-heading -->
                    <div id="chart-bar5" class="panel-body">
                        {{ chart.bar5|safe }}
                    </div>
                    <!-- /.panel-body -->
                </div>    
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <i class="fa fa-bar-chart-o fa-fw"></i> 24h慢查询db维度统计
                    </div>
                    <!-- /.panel-heading -->
                    <div id="chart-bar3" class="panel-body">
                        {{ chart.bar3|safe }}
                    </div>
                    <!-- /.panel-body -->
                </div>
            </div>
            <!-- /.col-lg-8 -->
            <div class="col-lg-4">
                <!-- /.panel -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <i class="fa fa-bar-chart-o fa-fw"></i> SQL上线统计
                    </div>
                    <!-- /.panel-heading -->
                    <div id="chart-pie1" class="panel-body">
                        {{ chart.pie1|safe }}
                    </div>
                    <!-- /.panel-body -->
                </div>
                <!-- /.panel -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <i class="fa fa-bar-chart-o fa-fw"></i> DB检索行数
                    </div>
                    <div id="chart-pie5" class="panel-body">
                        {{ chart.pie5|safe }}
                    </div>
                    <!-- /.panel-body -->
                </div>
                <!-- /.panel -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <i class="fa fa-bar-chart-o fa-fw"></i> SQL查询用户
                    </div>
                    <!-- /.panel-heading -->
                    <div id="chart-pie4" class="panel-body">
                        {{ chart.pie4|safe }}
                    </div>
                    <!-- /.panel-body -->
                </div>
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <i class="fa fa-bar-chart-o fa-fw"></i> SQL上线工单类型
                    </div>
                    <!-- /.panel-heading -->
                    <div id="chart-pie2" class="panel-body">{{ chart.pie2|safe }}</div>
                    <!-- /.panel-body -->
                </div>
    
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <i class="fa fa-bar-chart-o fa-fw"></i> 24h慢查询db/user维度统计
                    </div>
                    <!-- /.panel-heading -->
                    <div id="chart-pie3" class="panel-body">
                        {{ chart.pie3|safe }}
                    </div>
                    <!-- /.panel-body -->
                </div>
            </div>
            <!-- /.col-lg-4 -->
        </div>
        <!-- /.row -->
    {% endcache %}
{% endblock content %}

{% block js %}
    {% load static %}
    <link href="{% static 'daterangepicker/css/daterangepicker.css' %}" rel="stylesheet" type="text/css"/>
    <script src="{% static 'daterangepicker/js/moment.min.js' %}"></script>
    <script src="{% static 'daterangepicker/js/daterangepicker.js' %}"></script>
    <script src="{% static 'bootstrap-table/js/bootstrap-table-export.min.js' %}"></script>
    <script src="{% static 'bootstrap-table/js/tableExport.min.js' %}"></script>
    <script type="text/javascript">
        // 初始化时间控件
        $(function () {
            let start = moment().subtract('days', 6);
            let end = moment().subtract('days', -1);

            function cb(start, end) {
                if (start.isValid() && end.isValid()) {
                    $('#reservation span').html(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));
                } else {
                    $('#reservation span').html('全部数据');
                }
            }

            $('#reservation').daterangepicker({
                startDate: start,
                endDate: end,
                showDropdowns: true,
                locale: {
                    format: "YYYY-MM-DD",// 显示格式
                    separator: " / ",// 两个日期之间的分割线
                    // 中文化
                    applyLabel: "确定",
                    cancelLabel: "取消",
                    fromLabel: "开始",
                    toLabel: "结束",
                    customRangeLabel: "自定义",
                    daysOfWeek: ["日", "一", "二", "三", "四", "五", "六"],
                    monthNames: ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
                    firstDay: 1
                },
                ranges: {
                    "最近7日": [moment().subtract('days', 6), moment().subtract('days', -1)],
                    "最近30日": [moment().subtract('days', 29), moment().subtract('days', -1)],
                    "最近365日": [moment().subtract('days', 364), moment().subtract('days', -1)],
                    "本月": [moment().startOf("month"), moment().endOf("month").subtract('days', -1)],
                    "本年": [moment().startOf("year"), moment().endOf("year").subtract('days', -1)],
                    "上个月": [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month").subtract('days', -1)],
                    "上一年": [moment().subtract(1, "year").startOf("year"), moment().subtract(1, "year").endOf("year").subtract('days', -1)]
                }
            }, cb).on('apply.daterangepicker', function (ev, picker) {
                let startDate = picker.startDate.format('YYYY-MM-DD');
                let endDate = picker.endDate.format('YYYY-MM-DD');
                $.ajax({
                    url: '/dashboard/api/',
                    type: 'GET',
                    data: {
                        'start_date': startDate,
                        'end_date': endDate
                    },
                    success: function(response) {
                        updateChart(response);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error sending data for dashboard:', error);
                    }
                });
            });
            cb(start, end);
        });

        function updateChart(response) {
            $('#chart-line1').html(response.chart.line1);   // SQL查询统计
            $('#chart-bar1').html(response.chart.bar1);     // SQL上线数量
            $('#chart-bar2').html(response.chart.bar2);     // SQL上线用户
            $('#chart-bar3').html(response.chart.bar3);     // 慢查询db维度统计
            $('#chart-bar4').html(response.chart.bar4);     // 数据库实例环境统计
            $('#chart-bar5').html(response.chart.bar5);     // SQL上线工单
            $('#chart-pie1').html(response.chart.pie1);     // SQL上线统计
            $('#chart-pie2').html(response.chart.pie2);     // 数据库实例类型统计
            $('#chart-pie3').html(response.chart.pie3);     // 慢查询db/user维度统计
            $('#chart-pie4').html(response.chart.pie4);     // SQL查询用户
            $('#chart-pie5').html(response.chart.pie5);     // DB检索行数
        }

    </script>
{% endblock %}