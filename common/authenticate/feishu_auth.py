# coding: utf-8

import logging
from django.contrib.auth.backends import BaseBackend
from django.contrib.auth.models import Group
from django.core.exceptions import SuspiciousOperation
from sql.models import Users
from common.auth import init_user
from common.utils.feishu_oauth import FeishuOAuth

logger = logging.getLogger("default")


class FeishuAuthenticationBackend(BaseBackend):
    """飞书 OAuth 认证后端"""
    
    def authenticate(self, request, code=None, **kwargs):
        """通过飞书授权码进行认证"""
        if not code:
            return None
            
        feishu_oauth = FeishuOAuth()
        
        # 获取用户访问令牌
        token_data = feishu_oauth.get_user_access_token(code)
        if not token_data:
            logger.error("获取飞书用户访问令牌失败")
            return None
        
        user_access_token = token_data.get("access_token")
        if not user_access_token:
            logger.error("飞书用户访问令牌为空")
            return None
        
        # 获取用户信息
        user_info = feishu_oauth.get_user_info(user_access_token)
        if not user_info:
            logger.error("获取飞书用户信息失败")
            return None
        
        # 提取用户信息
        open_id = user_info.get("open_id")
        email = user_info.get("email")
        name = user_info.get("name")
        employee_id = user_info.get("employee_id")
        
        if not open_id or not email or not name:
            raise SuspiciousOperation("飞书用户信息不完整，缺少必要字段")
        
        # 使用邮箱前缀作为用户名，如果有工号则使用工号
        username = employee_id if employee_id else email.split("@")[0]
        
        # 查找或创建用户
        try:
            user = Users.objects.get(username=username)
            # 更新用户信息
            user.email = email
            user.display = name
            user.feishu_open_id = open_id
            user.save()
            logger.info(f"更新飞书用户信息: {username}")
        except Users.DoesNotExist:
            # 创建新用户
            user = self.create_user(username, email, name, open_id)
            logger.info(f"创建飞书新用户: {username}")
        
        # 同步用户部门信息
        self.sync_user_departments(user, open_id, feishu_oauth)
        
        return user
    
    def create_user(self, username, email, display, open_id):
        """创建新用户"""
        user = Users.objects.create_user(
            username=username,
            email=email,
            display=display,
            feishu_open_id=open_id
        )
        # 初始化用户（添加到默认资源组和权限组）
        init_user(user)
        return user
    
    def sync_user_departments(self, user, open_id, feishu_oauth):
        """同步用户部门信息，创建对应的Django组"""
        try:
            # 获取用户所在部门
            department_ids = feishu_oauth.get_user_departments(open_id)
            if not department_ids:
                logger.warning(f"用户 {user.username} 没有部门信息")
                return
            
            # 为每个部门创建组并添加用户
            for dept_id in department_ids:
                dept_path = feishu_oauth.get_department_path(dept_id)
                if dept_path:
                    # 创建或获取组
                    group_name = f"飞书-{dept_path}"
                    group, created = Group.objects.get_or_create(name=group_name)
                    
                    if created:
                        logger.info(f"创建飞书部门组: {group_name}")
                    
                    # 将用户添加到组
                    if not user.groups.filter(name=group_name).exists():
                        user.groups.add(group)
                        logger.info(f"将用户 {user.username} 添加到组 {group_name}")
                        
        except Exception as e:
            logger.error(f"同步用户部门信息失败: {e}")
    
    def get_user(self, user_id):
        """根据用户ID获取用户"""
        try:
            return Users.objects.get(pk=user_id)
        except Users.DoesNotExist:
            return None
